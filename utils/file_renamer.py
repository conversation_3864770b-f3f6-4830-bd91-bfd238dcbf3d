#!/usr/bin/env python3
"""
文件重命名工具 - 处理带括号数字的重复文件名

功能：对于类似 "20250223 宏观策略金工(1).docx" 这样的文件名，
如果去掉最后的带数字括号部分后没有重名文件，就重命名为去掉括号后的文件名。

作者：齐天大圣 AI Assistant
创建时间：2025-01-02
"""

import argparse
import logging
import re
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


class FileRenamer:
    """
    文件重命名器类
    
    遵循SOLID原则：
    - 单一职责：专门处理带括号数字的文件重命名
    - 开放封闭：可扩展支持更多文件名模式
    - 接口隔离：提供清晰的公共接口
    """
    
    def __init__(self, directory: Path, extensions: Optional[List[str]] = None, dry_run: bool = False):
        """
        初始化文件重命名器
        
        Args:
            directory: 要处理的目录路径
            extensions: 允许的文件扩展名列表，None表示所有文件
            dry_run: 是否为预览模式（不实际执行重命名）
        """
        self.directory = Path(directory)
        self.extensions = [ext.lower() for ext in extensions] if extensions else None
        self.dry_run = dry_run
        
        # 正则表达式模式：匹配文件名末尾的 "(数字)" 格式
        # 例如：匹配 "文件名(1).docx" 中的 "(1)" 部分
        self.pattern = re.compile(r'^(.+)\((\d+)\)(\.[^.]+)$')
        
        if not self.directory.exists():
            raise FileNotFoundError(f"目录不存在: {self.directory}")
        if not self.directory.is_dir():
            raise NotADirectoryError(f"路径不是目录: {self.directory}")
    
    def find_candidates(self) -> List[Tuple[Path, str, str]]:
        """
        查找所有符合重命名条件的文件
        
        Returns:
            List[Tuple[Path, str, str]]: 元组列表，包含 (原文件路径, 新文件名, 括号中的数字)
        """
        candidates = []
        
        logger.info(f"开始扫描目录: {self.directory}")
        
        for file_path in self.directory.iterdir():
            if not file_path.is_file():
                continue
            
            # 检查文件扩展名过滤
            if self.extensions and file_path.suffix.lower() not in self.extensions:
                continue
            
            # 检查文件名是否匹配模式
            match = self.pattern.match(file_path.name)
            if not match:
                continue
            
            base_name, number, extension = match.groups()
            new_filename = f"{base_name}{extension}"
            
            logger.debug(f"找到候选文件: {file_path.name} -> {new_filename}")
            candidates.append((file_path, new_filename, number))
        
        logger.info(f"找到 {len(candidates)} 个候选文件")
        return candidates
    
    def check_rename_safety(self, candidates: List[Tuple[Path, str, str]]) -> List[Tuple[Path, str, str]]:
        """
        检查重命名操作的安全性，过滤掉会产生冲突的文件
        
        Args:
            candidates: 候选文件列表
            
        Returns:
            List[Tuple[Path, str, str]]: 安全的重命名候选列表
        """
        safe_candidates = []
        
        for file_path, new_filename, number in candidates:
            new_file_path = file_path.parent / new_filename
            
            if new_file_path.exists():
                logger.warning(f"跳过 {file_path.name}: 目标文件 {new_filename} 已存在")
                continue
            
            logger.debug(f"安全检查通过: {file_path.name} -> {new_filename}")
            safe_candidates.append((file_path, new_filename, number))
        
        logger.info(f"通过安全检查的文件: {len(safe_candidates)} 个")
        return safe_candidates
    
    def preview_changes(self, safe_candidates: List[Tuple[Path, str, str]]) -> None:
        """
        预览将要进行的重命名操作
        
        Args:
            safe_candidates: 安全的重命名候选列表
        """
        if not safe_candidates:
            print("没有找到需要重命名的文件。")
            return
        
        print(f"\n将要执行的重命名操作 ({len(safe_candidates)} 个文件):")
        print("-" * 80)
        
        for i, (file_path, new_filename, number) in enumerate(safe_candidates, 1):
            print(f"{i:3d}. {file_path.name}")
            print(f"     -> {new_filename}")
            print(f"     (移除括号数字: ({number}))")
            print()
    
    def execute_rename(self, safe_candidates: List[Tuple[Path, str, str]]) -> Dict[str, int]:
        """
        执行实际的重命名操作
        
        Args:
            safe_candidates: 安全的重命名候选列表
            
        Returns:
            Dict[str, int]: 操作结果统计 {'success': 成功数, 'failed': 失败数}
        """
        results = {'success': 0, 'failed': 0}
        
        if not safe_candidates:
            logger.info("没有需要重命名的文件")
            return results
        
        logger.info(f"开始执行重命名操作，共 {len(safe_candidates)} 个文件")
        
        for file_path, new_filename, number in safe_candidates:
            try:
                new_file_path = file_path.parent / new_filename
                
                if self.dry_run:
                    logger.info(f"[预览模式] {file_path.name} -> {new_filename}")
                    results['success'] += 1
                else:
                    file_path.rename(new_file_path)
                    logger.info(f"✅ 重命名成功: {file_path.name} -> {new_filename}")
                    results['success'] += 1
                    
            except Exception as e:
                logger.error(f"❌ 重命名失败: {file_path.name} -> {new_filename}, 错误: {e}")
                results['failed'] += 1
        
        return results
    
    def process(self) -> Dict[str, int]:
        """
        执行完整的文件重命名流程
        
        Returns:
            Dict[str, int]: 操作结果统计
        """
        try:
            # 1. 查找候选文件
            candidates = self.find_candidates()
            
            # 2. 安全性检查
            safe_candidates = self.check_rename_safety(candidates)
            
            # 3. 预览更改
            if self.dry_run or not safe_candidates:
                self.preview_changes(safe_candidates)
            
            # 4. 执行重命名
            results = self.execute_rename(safe_candidates)
            
            return results
            
        except Exception as e:
            logger.error(f"处理过程中发生错误: {e}")
            return {'success': 0, 'failed': 1}


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(
        description="文件重命名工具 - 处理带括号数字的重复文件名",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s /path/to/directory                    # 处理目录中的所有文件
  %(prog)s /path/to/directory --dry-run          # 预览模式，不实际重命名
  %(prog)s /path/to/directory --extensions .docx .pdf  # 只处理指定类型的文件
  %(prog)s /path/to/directory --verbose          # 详细输出模式
        """
    )
    
    parser.add_argument(
        'directory',
        type=str,
        help='要处理的目录路径'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='预览模式，显示将要进行的操作但不实际执行'
    )
    
    parser.add_argument(
        '--extensions',
        nargs='+',
        help='指定要处理的文件扩展名（如 .docx .pdf .txt）'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出模式'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # 创建文件重命名器实例
        renamer = FileRenamer(
            directory=args.directory,
            extensions=args.extensions,
            dry_run=args.dry_run
        )
        
        # 执行处理
        results = renamer.process()
        
        # 输出结果摘要
        print(f"\n操作完成:")
        print(f"  成功: {results['success']} 个文件")
        print(f"  失败: {results['failed']} 个文件")
        
        if args.dry_run:
            print("\n注意: 这是预览模式，没有实际执行重命名操作。")
            print("要执行实际操作，请移除 --dry-run 参数。")
        
        # 设置退出码
        sys.exit(0 if results['failed'] == 0 else 1)
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()

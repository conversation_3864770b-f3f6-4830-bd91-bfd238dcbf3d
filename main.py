#!/usr/bin/env python3
"""
AI驱动的自动化视频剪辑项目主程序
支持从任意阶段开始执行的模块化处理流程
"""

import argparse
import importlib
import inspect
import json
import os  # 新增此行
import re
import sys
import tempfile  # 新增此行
from pathlib import Path
from typing import Optional, List, Dict, Any

from config.settings import settings
from database.models import DatabaseManager
from stages.base import BaseStage
from utils.ai_utils import primary_client  # 导入 primary_client
from utils.logger import get_logger
from utils.shared_state import shutdown_event
from utils.video_utils import video_processor

logger = get_logger(__name__)


class AutoCutter:
    """自动化视频剪辑主控制器"""

    def __init__(self):
        self.db_manager = DatabaseManager()
        self.stages = self._load_stages()
        self.max_stage_number = max(self.stages.keys()) if self.stages else 0
        logger.info(f"已动态加载 {len(self.stages)} 个阶段，最高阶段编号: {self.max_stage_number}")

    def _load_stages(self) -> dict:
        """动态加载 stages 目录下的所有阶段类"""
        stages_dir = Path(__file__).parent / "stages"
        stage_classes = {}

        # 筛选出 stage*.py 文件并排序，确保加载顺序稳定
        for file in sorted(stages_dir.glob("stage_*.py")):
            module_name = f"stages.{file.stem}"
            try:
                module = importlib.import_module(module_name)
                for name, obj in inspect.getmembers(module, inspect.isclass):
                    # 检查是否是 BaseStage 的子类，并且不是 BaseStage 本身
                    if issubclass(obj, BaseStage) and obj is not BaseStage:
                        # 创建一个临时实例以获取 stage_number
                        # 注意：这里传递 None 是因为我们只关心 stage_number 属性
                        temp_instance = obj(db_manager=None, video_id=-1)  # type: ignore
                        stage_num = temp_instance.stage_number
                        if stage_num in stage_classes:
                            logger.error(f"发现重复的阶段编号 {stage_num}。模块 {module_name} 与之前的模块存在冲突。")
                            raise ValueError(f"阶段编号 {stage_num} 重复")
                        stage_classes[stage_num] = obj
            except Exception as e:
                logger.error(f"加载阶段模块 {module_name} 失败: {e}")

        return stage_classes

    def list_videos(self):
        """列出所有视频记录"""
        videos = self.db_manager.get_all_videos()

        if not videos:
            print("没有找到任何视频记录")
            return

        print("\n视频列表:")
        print("-" * 95)
        print(f"{'ID':<5} {'视频名称':<30} {'时长(秒)':<10} {'哈希值(前12位)':<15} {'状态':<15} {'创建时间':<20}")
        print("-" * 95)

        for video in videos:
            # 安全地获取哈希值并截取前12位
            file_hash_short = (video.get("file_hash") or "N/A")[:12]
            # 将 datetime 对象安全地转换为字符串，并截取以保证对齐
            created_at_str = str(video.get("created_at", "N/A"))[:19]

            print(
                f"{video['id']:<5} {video['video_name']:<30} {video['total_duration']:<10.1f} {file_hash_short:<15} {video['status']:<15} {created_at_str:<20}"
            )

    def show_status(self, video_id: int):
        """显示视频处理状态"""
        # 获取视频信息
        video = self.db_manager.get_video_by_id(video_id)
        if not video:
            print(f"未找到ID为 {video_id} 的视频")
            return

        print(f"\n视频信息: {video['video_name']}")
        print("-" * 60)
        print(f"时长: {video['total_duration']:.1f} 秒")
        print(f"全局摘要: {video['global_summary'] or '未生成'}")
        print(f"关键主题: {video['key_themes'] or '未生成'}")

        # 获取各阶段状态
        print("\n处理阶段状态:")
        print("-" * 60)

        for stage_num in range(1, self.max_stage_number + 1):
            status = self.db_manager.get_stage_status(video_id, stage_num)
            if status:
                status_text = status["status"]
                if status["started_at"]:
                    status_text += f" (开始: {str(status['started_at'])[:19]})"
                if status["completed_at"]:
                    status_text += f" (完成: {str(status['completed_at'])[:19]})"
                if status["error_message"]:
                    status_text += f" (错误: {status['error_message']})"

                print(f"阶段 {stage_num}: {status['stage_name']} - {status_text}")
            else:
                print(f"阶段 {stage_num}: 未找到状态记录")

    def run_stage(self, video_id: int, stage_number: int, force_level: Optional[str] = None, **kwargs) -> bool:
        """运行指定阶段"""
        if stage_number not in self.stages:
            logger.error(f"无效的阶段编号: {stage_number}")
            return False

        # 检查视频是否存在
        if not self.db_manager.get_video_by_id(video_id):
            logger.error(f"未找到ID为 {video_id} 的视频")
            return False

        # 创建阶段实例
        stage_class = self.stages[stage_number]
        stage = stage_class(self.db_manager, video_id)

        logger.info(f"开始运行阶段 {stage_number}: {stage.stage_name}")

        # 【核心改动】: 将 force_level 作为独立的关键字参数传递给 run 方法
        # 同时将其他参数通过 **kwargs 传递
        success = stage.run(force_level=force_level, **kwargs)

        if success:
            logger.info(f"阶段 {stage_number} 运行成功")
        else:
            logger.error(f"阶段 {stage_number} 运行失败")

        return success

    def run_all_stages(
        self,
        video_id: int,
        start_from: int = 1,
        end_at: Optional[int] = None,
        force_level: Optional[str] = None,
        **kwargs,
    ) -> bool:
        """运行所有阶段"""
        if end_at is None:
            end_at = self.max_stage_number  # 更新为新的最大阶段号

        logger.info(f"开始运行所有阶段，从阶段 {start_from} 到阶段 {end_at}。")
        if force_level:
            logger.info(f"--- 范围强制执行模式已激活 (级别: {force_level}) ---")

        # 按阶段编号排序执行
        for stage_number in sorted(self.stages.keys()):
            if stage_number < start_from:
                continue
            if stage_number > end_at:
                logger.info(f"已达到结束阶段 {end_at}，停止运行后续阶段。")
                break

            if shutdown_event.is_set():
                logger.warning("检测到关闭信号，停止处理后续阶段。")
                return False

            # 【核心修改】将 force_level 和其他 kwargs 传递给 run_stage
            if not self.run_stage(video_id, stage_number, force_level=force_level, **kwargs):
                logger.error(f"阶段 {stage_number} 失败，停止后续处理。")
                return False

        logger.info("所有指定阶段运行完成。")
        return True

    def search_shots(self, video_id: int, query: str, top_k: int = 5):
        """根据自然语言查询，在视频中搜索最相关的镜头。"""
        logger.info(f"开始为视频ID {video_id} 执行语义搜索，查询: '{query}'。")

        # 1. 获取查询的向量
        logger.info("正在为查询生成嵌入向量...")
        query_vector = primary_client.get_embedding(query)
        if query_vector is None:
            logger.error("无法为查询生成嵌入向量，搜索中止。")
            return False

        # 3. 调用数据库管理器执行搜索
        logger.info("正在数据库中执行向量搜索...")
        results = self.db_manager.search_shots_by_semantic(video_id, query_vector, top_k)

        # 4. 打印结果
        if not results:
            print("\n未找到相关镜头。")
            return True

        print(f"\n找到 Top {len(results)} 个最相关的镜头:")
        print("-" * 100)
        print(f"{'相似度':<8} {'时间段':<18} {'描述'}")
        print("-" * 100)
        for res in results:
            similarity_str = f"{res['similarity']:.2%}"
            time_range = f"{res['start_time']:.2f}s - {res['end_time']:.2f}s"
            description = res.get("visual_description", "N/A")
            print(f"{similarity_str:<8} {time_range:<18} {description}")

        return True

    def import_videos(self):
        """从输入目录扫描并导入新视频"""
        logger.info(f"开始从目录 '{settings.INPUT_DIR}' 导入视频...")
        supported_formats = [".mp4", ".mov", ".avi", ".mkv"]
        video_files = []
        for fmt in supported_formats:
            video_files.extend(settings.INPUT_DIR.glob(f"*{fmt}"))

        if not video_files:
            logger.warning("在输入目录中没有找到支持的视频文件。")
            return

        video_files.sort()
        imported_count = 0
        for video_file in video_files:
            video_path_str = str(video_file.resolve())

            logger.info(f"发现新视频: {video_file.name}, 开始计算哈希值...")
            try:
                file_hash = video_processor.calculate_file_hash(video_file)
            except Exception as e:
                logger.error(f"无法计算 '{video_file.name}' 的哈希值，跳过。错误: {e}")
                continue

            # 获取当前代码中定义的所有阶段信息
            stages_info = {
                num: cls(db_manager=None, video_id=-1).stage_name
                for num, cls in self.stages.items()  # type: ignore
            }

            # 检查视频是否已存在
            existing_video = self.db_manager.get_video_by_hash(file_hash)
            if existing_video:
                video_id = existing_video["id"]
                logger.info(f"视频 '{video_file.name}' 已存在于数据库 (ID: {video_id})。正在检查并同步阶段状态...")
                # 为已存在的视频同步阶段状态
                self.db_manager.sync_stage_statuses(video_id, stages_info)
                continue  # 处理下一个文件

            logger.info(f"开始提取视频信息: {video_file.name}...")
            video_info = video_processor.get_video_info(video_file)
            if not video_info:
                logger.error(f"无法获取 '{video_file.name}' 的视频信息，跳过。")
                continue

            fps = video_info.get("fps")
            if not fps or fps <= 0:
                logger.warning(f"无法自动检测到视频 '{video_file.name}' 的有效FPS，需要手动输入。")

                common_fps_options = ["23.976", "24", "25", "29.97", "30", "50", "59.94", "60"]
                print("\n请选择或输入视频的帧率 (FPS):")
                for i, option in enumerate(common_fps_options):
                    print(f"  {i + 1}: {option}")
                print("  或者直接输入一个自定义的数值 (例如: 23.98)")

                while True:
                    try:
                        choice = input("请输入你的选择: ").strip()
                        if choice.isdigit() and 1 <= int(choice) <= len(common_fps_options):
                            fps = float(common_fps_options[int(choice) - 1])
                            break
                        else:
                            # 尝试直接将输入解析为浮点数
                            custom_fps = float(choice)
                            if custom_fps > 0:
                                fps = custom_fps
                                break
                            else:
                                print("无效输入，请输入一个正数。")
                    except ValueError:
                        print("无效输入，请输入一个有效的数字选项或FPS值。")

                logger.info(f"用户已为视频 '{video_file.name}' 指定FPS为: {fps}")
                video_info["fps"] = fps  # 更新 video_info 字典

            # 步骤1: 创建视频记录
            video_id = self.db_manager.create_video_record(
                video_name=video_file.stem,
                total_duration=video_info["duration"],
                fps=fps,  # 使用已验证和处理过的局部变量 fps
                file_hash=file_hash,
                input_file_path=video_path_str,
            )

            # 步骤2: 基于动态加载的阶段信息，初始化状态记录
            self.db_manager.initialize_stage_statuses(video_id, stages_info)

            logger.info(f"✅ 成功导入视频 '{video_file.stem}'，ID: {video_id}，时长: {video_info['duration']:.1f}秒。")
            imported_count += 1

        logger.info(f"导入完成。新增 {imported_count} 个视频。")

    def name_characters(self, video_id: int, character_id: Optional[int] = None):
        """为指定视频提供角色命名功能，支持直接重命名或交互式队列命名。"""
        # --- 通用设置：加载演员表和创建临时目录 ---
        logger.info(f"开始为视频ID {video_id} 进行角色命名...")
        session_dir = Path(tempfile.gettempdir()) / f"autocutter_naming_session_{video_id}"
        session_dir.mkdir(exist_ok=True, parents=True)
        logger.info(f"角色代表性人脸图片将保存在: {session_dir}")

        cast_list = []
        video_info = self.db_manager.get_video_by_id(video_id)
        video_hash = video_info.get("file_hash") if video_info else None
        if video_hash:
            video_output_dir = settings.OUTPUT_DIR / video_hash
            cast_list_path = video_output_dir / "cast_list.json"
            if cast_list_path.exists():
                with open(cast_list_path, "r", encoding="utf-8") as f:
                    cast_list = json.load(f)
        else:
            logger.warning(f"视频ID {video_id} 缺少文件哈希值，无法加载演员表建议。")

        # --- 模式1: 直接重命名指定ID的角色 ---
        if character_id:
            logger.info(f"进入直接重命名模式，目标角色ID: {character_id}")
            character_group = self.db_manager.get_naming_info_for_character(video_id, character_id)

            if not character_group:
                logger.error(f"未找到属于视频ID {video_id} 的角色ID {character_id} 的信息，或该角色无人脸数据。")
                return False

            # 复用交互式模式的UI和逻辑，但只执行一次
            self._process_single_character_naming(character_group, cast_list, session_dir)
            return True

        # --- 模式2: 交互式队列命名 (原有逻辑) ---
        while True:
            characters_to_name = self.db_manager.get_characters_for_naming(video_id)
            if not characters_to_name:
                logger.info("所有角色均已命名，或没有需要命名的角色。")
                break

            grouped_chars = {}
            for char_data in characters_to_name:
                char_id_key = char_data["character_id"]
                if char_id_key not in grouped_chars:
                    grouped_chars[char_id_key] = []
                grouped_chars[char_id_key].append(char_data)

            char_id_to_process = list(grouped_chars.keys())[0]
            character_group_to_process = grouped_chars[char_id_to_process]

            should_continue = self._process_single_character_naming(character_group_to_process, cast_list, session_dir)
            if not should_continue:
                break

        logger.info("交互式角色命名会话结束。")
        return True

    def _process_single_character_naming(
        self, character_group: List[Dict[str, Any]], cast_list: List[Dict[str, Any]], session_dir: Path
    ) -> bool:
        """处理单个角色的命名UI和逻辑，返回是否应继续循环（仅对交互模式有意义）。"""
        if not character_group:
            return False

        first_entry = character_group[0]
        char_id_to_process = first_entry["character_id"]

        os.system("cls" if os.name == "nt" else "clear")
        print("=" * 80)
        # 【核心修改】显示当前名称，无论是否是“未知角色”
        print(f"正在命名角色 ID: {char_id_to_process} (当前名称: {first_entry['character_name']})")
        print(f"(在视频中检测到 {first_entry['face_count']} 次)")
        print("-" * 80)

        print("代表性人脸 (已保存到临时目录供查看):")
        for i, face_info in enumerate(character_group):
            face_image_path = session_dir / f"char_{face_info['character_id']}_face_{i + 1}.jpg"
            video_path = Path(face_info["clip_url"])
            if video_path.exists():
                video_processor.extract_and_save_face_image(
                    video_path=video_path,
                    time_seconds=face_info["time_offset_in_shot"],
                    bounding_box=face_info["bounding_box"],
                    output_path=face_image_path,
                )
                print(f"  - 人脸 {i + 1}: {face_image_path}")
                print(f"    来源: {video_path.name} @ {face_info['time_offset_in_shot']:.2f}s")
                print(f"    场景描述: {face_info.get('visual_description', 'N/A')}")
                if face_info.get("dialogue"):
                    print(f"    附近对白: {face_info.get('dialogue')}")
            else:
                print(f"  - 人脸 {i + 1} 的源视频片段丢失: {video_path}")
        print("-" * 80)

        suggestion = first_entry.get("suggested_name_info")
        if suggestion:
            # 优先使用 display_name，如果不存在则回退到 name，以兼容旧数据
            display_name = suggestion.get("display_name", suggestion.get("name", "未知建议"))
            print(f"AI 建议: \033[93m{display_name}\033[0m (基于人脸匹配，距离: {suggestion['distance']:.4f})")
            print("  -> 输入 'accept' 接受此建议。")

        if cast_list:
            print("\n从演职员表中提取的可用名称:")
            num_items = len(cast_list)
            for i in range(0, num_items, 2):
                celeb1 = cast_list[i]
                role_type_display1 = f"{celeb1.get('role_type', '')}, " if celeb1.get("role_type") else ""
                actor_info1 = f"({role_type_display1}演员: {celeb1['actor_name']})"
                col1_str = f"  {i + 1:2d}. {celeb1['character_name']} {actor_info1}"
                col2_str = ""
                if (i + 1) < num_items:
                    celeb2 = cast_list[i + 1]
                    role_type_display2 = f"{celeb2.get('role_type', '')}, " if celeb2.get("role_type") else ""
                    actor_info2 = f"({role_type_display2}演员: {celeb2['actor_name']})"
                    col2_str = f"{i + 2:2d}. {celeb2['character_name']} {actor_info2}"
                print(f"{col1_str:<60}{col2_str}")
            print("\n  -> 输入 'name <编号>' 或 'name <自定义名称>' 来命名。")

        print("\n可用命令:")
        print("  name (n) <名称或编号>   # 为此角色命名")
        if suggestion:
            print("  accept (a)              # 接受AI建议")
        print("  skip (s)                # 跳过此角色 (仅交互模式)")
        print("  delete (del)            # 删除此角色 (及其所有关联人脸)")
        print("  quit (q)                # 退出命名会话")

        try:
            user_input = input("\n请输入命令: ").strip()
            if not user_input:
                return True

            parts = user_input.split(" ", 1)
            command = parts[0].lower()

            if command in ["quit", "q"]:
                return False
            elif command in ["delete", "del"]:
                confirm = input(
                    f"确定要永久删除角色 ID {char_id_to_process} 吗？这将取消其所有关联人脸的分配。 (y/n): "
                ).lower()
                if confirm == "y":
                    if self.db_manager.delete_character_and_unassign_faces(char_id_to_process):
                        logger.info(f"角色 ID {char_id_to_process} 已成功删除。")
                    else:
                        logger.error(f"删除角色 ID {char_id_to_process} 失败。")
                else:
                    logger.info("删除操作已取消。")
                return True
            elif command in ["skip", "s"]:
                logger.info(f"已跳过角色 ID: {char_id_to_process}")
                self.db_manager.update_character_name(char_id_to_process, f"已跳过_{char_id_to_process}")
                return True
            elif command in ["accept", "a"] and suggestion:
                self.db_manager.update_character_name(
                    char_id_to_process, suggestion["name"], source="suggested_accepted"
                )
                logger.info(f"已接受建议，角色 {char_id_to_process} 已命名为: {suggestion['name']}")
            elif command in ["name", "n"]:
                if len(parts) < 2:
                    print("错误: 'name' 命令需要一个参数。")
                    return True
                name_arg = parts[1]
                final_name = ""
                if name_arg.isdigit() and cast_list and 1 <= int(name_arg) <= len(cast_list):
                    celeb = cast_list[int(name_arg) - 1]
                    final_name = celeb["character_name"]
                else:
                    final_name = name_arg
                self.db_manager.update_character_name(char_id_to_process, final_name, source="manual_rename")
                logger.info(f"角色 {char_id_to_process} 已由用户命名为: {final_name}")
            else:
                print(f"错误: 未知命令或无效操作 '{user_input}'")

        except (KeyboardInterrupt, EOFError):
            print("\n操作被中断。")
            return False
        except Exception as e:
            logger.error(f"处理命令时发生错误: {e}", exc_info=True)
        return True

    def review_causal_graph(self, video_id: int):
        """为指定视频提供交互式因果图谱审查和编辑界面"""
        logger.info(f"开始为视频ID {video_id} 进行交互式因果图谱审查...")
        # 从数据库加载精炼后的因果图谱
        graph_data = self.db_manager.get_stage_output(video_id, 15, "refined_causal_graph")

        if not graph_data:
            logger.error("未找到精炼后的因果图谱数据。")
            logger.error("请先运行到阶段15 (D2S Reader 图谱精炼) 以生成该数据。")
            return False

        nodes = graph_data.get("nodes", [])
        edges = graph_data.get("edges", [])
        node_ids = {node["event_id"] for node in nodes}

        def print_graph():
            print("\n" + "=" * 80)
            print("当前因果图谱:")
            print("-" * 80)
            print("节点 (事件):")
            for node in nodes:
                print(f"  - {node['event_id']}: {node['event_description']}")
            print("\n边 (因果关系):")
            if not edges:
                print("  (暂无因果关系)")
            else:
                for i, edge in enumerate(edges):
                    print(
                        f"  {i + 1}. {edge['source_event_id']} -> {edge['target_event_id']}: {edge['causality_description']}"
                    )
            print("=" * 80)

        while True:
            print_graph()
            print("\n可用命令:")
            print('  add (a) <source_id> <target_id> "<description>"   # 添加一条边')
            print("  delete (d) <edge_number>                         # 删除一条边 (按上方列表编号)")
            print("  save (s)                                         # 保存修改并退出")
            print("  quit (q)                                         # 不保存修改并退出")

            try:
                command_line = input("\n请输入命令: ").strip()
                if not command_line:
                    continue

                parts = command_line.split(" ", 1)
                command = parts[0].lower()

                if command in ["quit", "q"]:
                    logger.info("用户选择不保存退出。")
                    break

                elif command in ["save", "s"]:
                    self.db_manager.save_stage_output(
                        video_id=video_id,
                        stage_number=15,  # 阶段15是精炼图谱的阶段
                        stage_name="D2S Reader (图谱精炼)",
                        output_type="refined_causal_graph",
                        output_data={"nodes": nodes, "edges": edges},
                    )
                    logger.info("图谱已成功保存到数据库。")
                    break

                elif command in ["delete", "d"]:
                    if len(parts) < 2:
                        logger.error("删除命令格式错误。用法: delete <edge_number>")
                        continue
                    try:
                        edge_num = int(parts[1])
                        if 1 <= edge_num <= len(edges):
                            removed_edge = edges.pop(edge_num - 1)
                            logger.info(
                                f"已移除边: {removed_edge['source_event_id']} -> {removed_edge['target_event_id']}"
                            )
                        else:
                            logger.error("无效的边编号。")
                    except ValueError:
                        logger.error("边编号必须是一个数字。")

                elif command in ["add", "a"]:
                    # 使用正则表达式来正确解析带引号的描述
                    match = re.match(r'(\S+)\s+(\S+)\s+"([^"]+)"', parts[1])
                    if not match:
                        logger.error('添加命令格式错误。用法: add <source_id> <target_id> "<description>"')
                        continue

                    source_id, target_id, description = match.groups()

                    if source_id not in node_ids or target_id not in node_ids:
                        logger.error("源节点或目标节点ID不存在。")
                        continue

                    new_edge = {
                        "source_event_id": source_id,
                        "target_event_id": target_id,
                        "causality_description": description,
                    }
                    edges.append(new_edge)
                    logger.info(f"已添加新边: {source_id} -> {target_id}")

                else:
                    logger.error(f"未知命令: '{command}'")

            except (KeyboardInterrupt, EOFError):
                print("\n操作被中断。")
                break
            except Exception as e:
                logger.error(f"处理命令时发生错误: {e}", exc_info=True)

        return True

    def refine_script(self, video_id: int):
        """为指定视频提供交互式剧本修订界面"""
        logger.warning(
            "注意：由于工作流已更新为'先视觉后旁白'的模式，交互式剧本修订功能 (`refine_script`) 正在重构中，当前版本已临时禁用。"
        )
        return True


def main():
    """主函数，使用 argparse 定义和处理命令行参数"""

    # 提前实例化以获取动态阶段信息
    auto_cutter = AutoCutter()
    max_stage = auto_cutter.max_stage_number  # 更新为新的最大阶段号

    # --- 1. 创建主解析器 ---
    parser = argparse.ArgumentParser(
        description="AI驱动的自动化视频剪辑工作流",
        formatter_class=argparse.RawTextHelpFormatter,  # 使用 RawTextHelpFormatter 以保留epilog中的格式
    )
    subparsers = parser.add_subparsers(dest="command", required=True, help="可用的子命令")

    # --- 2. 定义 'list' 子命令 ---
    list_parser = subparsers.add_parser("list", help="列出所有已导入的视频")
    list_parser.set_defaults(func=lambda args: auto_cutter.list_videos())

    # --- 3. 定义 'status' 子命令 ---
    status_parser = subparsers.add_parser("status", help="显示指定视频的处理状态")
    status_parser.add_argument("-v", "--video", dest="video_id", type=int, required=True, help="要查询状态的视频ID")
    status_parser.set_defaults(func=lambda args: auto_cutter.show_status(args.video_id))

    # --- 4. 定义 'import' 子命令 ---
    import_parser = subparsers.add_parser("import", help="从输入目录扫描并导入新视频")
    import_parser.set_defaults(func=lambda args: auto_cutter.import_videos())

    # --- 5. 定义 'name' 子命令 ---
    name_parser = subparsers.add_parser("name", help="为指定视频交互式地命名角色")
    name_parser.add_argument("-v", "--video", dest="video_id", type=int, required=True, help="要命名角色的视频ID")
    name_parser.add_argument(
        "-i", "--id", dest="character_id", type=int, required=False, help="直接指定要重命名的角色数据库ID"
    )
    name_parser.set_defaults(
        func=lambda args: auto_cutter.name_characters(args.video_id, character_id=args.character_id)
    )

    # --- 新增: 定义 'review_graph' 子命令 ---
    review_parser = subparsers.add_parser("review_graph", help="交互式地审查和编辑因果图谱")
    review_parser.add_argument("-v", "--video", dest="video_id", type=int, required=True, help="要审查图谱的视频ID")
    review_parser.set_defaults(func=lambda args: auto_cutter.review_causal_graph(args.video_id))

    # --- 新增: 定义 'refine_script' 子命令 ---
    refine_parser = subparsers.add_parser("refine_script", help="交互式地修订已生成的剧本")
    refine_parser.add_argument("-v", "--video", dest="video_id", type=int, required=True, help="要修订剧本的视频ID")
    refine_parser.set_defaults(func=lambda args: auto_cutter.refine_script(args.video_id))

    # --- 新增: 定义 'search' 子命令 ---
    search_parser = subparsers.add_parser("search", help="通过自然语言语义搜索视频中的镜头")
    search_parser.add_argument("-v", "--video", dest="video_id", type=int, required=True, help="要搜索的视频ID")
    search_parser.add_argument("-q", "--query", type=str, required=True, help="你的自然语言搜索查询")
    search_parser.add_argument("-k", "--top-k", type=int, default=5, help="返回最相关结果的数量 (默认: 5)")
    search_parser.set_defaults(func=lambda args: auto_cutter.search_shots(args.video_id, args.query, args.top_k))

    # --- 6. 定义 'run' 子命令 (核心执行命令) ---
    run_parser = subparsers.add_parser(
        "run",
        help="运行一个或多个处理阶段",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  main.py run --video 1 --stage 1                      # 运行视频1的阶段1
  main.py run -v 1 -s all                              # 运行视频1的所有阶段 (使用短参数)
  main.py run --video 1 --stage all --from 12           # 从阶段12(角色档案生成)开始，运行后续所有阶段
  main.py run --video 1 --stage 20 --force full --tts-voice xiaoxiao
""",
    )
    run_parser.add_argument("-v", "--video", dest="video_id", type=int, required=True, help="要处理的视频ID")
    run_parser.add_argument(
        "-s",
        "--stage",
        dest="stage",
        required=True,
        help=f"要运行的阶段编号 ({min(auto_cutter.stages.keys())}-{max_stage}) 或 'all'",
    )
    run_parser.add_argument(
        "--from",
        type=int,
        dest="start_from",
        default=1,
        help="当运行 'all' 阶段时，从指定阶段开始 (默认: 1)",
    )
    run_parser.add_argument(
        "--to",
        type=int,
        dest="end_at",
        default=max_stage,
        help=f"当运行 'all' 阶段时，在指定阶段结束 (默认: {max_stage})",
    )
    run_parser.add_argument(
        "--force",
        "-f",
        nargs="?",
        const="soft",
        choices=["soft", "full", "vectorize"],
        help="强制运行一个阶段。\n'soft': 重新分析，但可能重用本地文件(如视频片段)。\n'full': 完全重新运行，删除所有本地文件和缓存。\n'vectorize': [仅阶段1] 仅重新生成所有镜头的文本向量。",
    )
    # 添加通用参数
    run_parser.add_argument(
        "--skip-until",
        type=float,
        default=0.0,
        help="通用参数：跳过视频开头指定秒数。对阶段1，跳过场景分析；对阶段2，跳过字幕条目。",
    )
    run_parser.add_argument(
        "--skip-after",
        type=float,
        help="通用参数：跳过视频指定秒数之后的内容。对阶段1，跳过后续的场景分析。",
    )

    # 添加阶段2的专属参数
    transcription_group = run_parser.add_argument_group("阶段2 (音频转写) 专属参数")
    transcription_group.add_argument(
        "--subtitles",
        type=Path,
        help="提供一个SRT/ASS字幕文件路径，将跳过自动转写流程",
    )

    # 添加研究阶段的专属参数
    research_group = run_parser.add_argument_group("阶段3 (外部研究) 专属参数")
    research_group.add_argument(
        "-g",
        "--genre",
        choices=["personal", "action", "drama", "comedy"],
        default="personal",
        help="视频类型/风格。'personal'跳过研究，'action'/'drama'/'comedy'会触发研究并使用特定分析模型 (默认: personal)",
    )
    research_group.add_argument("--title", type=str, help="视频的正式标题/片名")
    research_group.add_argument("--year", type=int, help="发行年份")
    research_group.add_argument("--actors", type=str, help="主要演员，用逗号分隔")
    research_group.add_argument("--director", type=str, help="导演")
    # --- 新增参数 ---
    research_group.add_argument("--topic", type=str, help="[仅--force soft] 指定一个要强制重新研究的特定主题字符串")
    # --- 新增结束 ---

    # 添加阶段6的专属参数
    labeling_group = run_parser.add_argument_group("阶段6 (角色自动标注) 专属参数")
    labeling_group.add_argument(
        "--cast-list-url",
        type=str,
        help="提供一个演职员表页面的URL (支持 douban, imdb, themoviedb)，将直接使用此URL进行角色自动标注。",
    )

    # --- 新增：创作指导参数 ---
    creative_group = run_parser.add_argument_group("阶段8+ (D2S工作流) 专属参数")
    creative_group.add_argument("--target-audience", type=str, help="目标受众与传播渠道")
    creative_group.add_argument("--visual-style", type=str, help="视觉风格与色彩基调 (例如: 写实、梦幻、复古)")
    creative_group.add_argument(
        "--narrative-rhythm", type=str, help="叙事节奏与剪辑手法 (例如: 快节奏蒙太奇、慢节奏情感渲染)"
    )
    creative_group.add_argument("--target-duration", type=str, help="成片的目标时长 (例如: '3-5分钟')")
    creative_group.add_argument(
        "--platform",
        type=str,
        choices=["douyin", "bilibili"],
        default="bilibili",
        help="目标发布平台，用于优化文案风格和节奏 (默认: bilibili)",
    )
    # 【新增】
    creative_group.add_argument(
        "--narration-perspective",
        type=str,
        choices=["first_person", "third_person"],
        default="third_person",
        help="指定全局解说的叙事视角 (第一人称 'first_person' 或 第三人称 'third_person') (默认: third_person)",
    )
    creative_group.add_argument(
        "--narration-style-preset",
        type=str,
        choices=["dialogue_heavy", "balanced", "narration_heavy"],
        default="balanced",
        help="选择旁白风格预设，以控制旁白和原声对话的比例 (默认: balanced)",
    )

    # 添加生产阶段的专属参数
    tts_group = run_parser.add_argument_group("阶段16 (成片合成) 专属参数")
    tts_group.add_argument("--tts-voice", choices=["xiaoxiao", "yunxi"], help="选择TTS发音人")

    def handle_run_command(args):
        """处理 'run' 子命令的逻辑"""

        # 直接从args命名空间创建kwargs字典，更简洁
        kwargs = vars(args)
        video_id = kwargs.pop("video_id", None)  # 从kwargs中提取video_id，这样它就不会被重复传递
        force_level = kwargs.pop("force", None)  # 从kwargs中提取force_level
        # --- 【核心修改】因为 start_from 和 end_at 也作为位置参数传递，所以一并移除 ---
        start_from = kwargs.pop("start_from", 1)
        end_at = kwargs.pop("end_at", None)

        if video_id is None:
            logger.error("未能从参数中获取 video_id。")
            sys.exit(1)

        if args.stage == "all":
            # 【核心修改】使用提取出的变量进行调用
            return auto_cutter.run_all_stages(video_id, start_from, end_at, force_level=force_level, **kwargs)
        else:
            try:
                stage_number = int(args.stage)
                if stage_number not in auto_cutter.stages:
                    raise ValueError()
                return auto_cutter.run_stage(video_id, stage_number, force_level=force_level, **kwargs)
            except ValueError:
                logger.error(f"阶段编号必须是 {list(sorted(auto_cutter.stages.keys()))} 中的一个或 'all'。")
                sys.exit(1)

    run_parser.set_defaults(func=handle_run_command)

    # --- 7. 解析参数并执行 ---
    args = parser.parse_args()

    # 验证配置
    config_errors = settings.validate()
    if config_errors:
        logger.error("配置验证失败:")
        for error in config_errors:
            logger.error(f"  - {error}")
        sys.exit(1)

    try:
        # 调用与子命令关联的函数
        success = args.func(args)
        if success is not None and not success:
            print("处理失败")
            sys.exit(1)
        elif success:
            print("处理完成")

    except InterruptedError:
        logger.warning("操作被用户中断。")
    except Exception as e:
        logger.error(f"程序执行出错: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()

"""
AI JSON输出的Pydantic模型定义
用于强制AI返回结构化的、可预测的、经过验证的对象。
"""

from enum import Enum
from typing import Any, List, Optional

from pydantic import BaseModel, Field, model_validator


# --- 新增：单个视觉节拍模型 ---
class ModelName(str, Enum):
    DOUBAO_SEED_1_6 = "doubao-seed-1-6-250615"
    DOUBAO_SEED_1_6_THINKING = "doubao-seed-1-6-thinking-250715"
    DOUBAO_SEED_1_6_FLASH = "doubao-seed-1-6-flash-250715"
    DEEPSEEK_R1 = "deepseek-r1-250528"
    O3_2025_04_16 = "o3-2025-04-16"
    KIMI_K2 = "kimi-k2-0711-preview"
    GLM_4_5 = "ZhipuAI/GLM-4.5"
    GROK_3_DEEPSEARCH = "grok-3-deepsearch"


# --- 新增：单个视觉节拍模型 ---
class VisualBeat(BaseModel):
    source_scene_number: int = Field(..., description="此视觉节拍对应的源场景编号。")
    description: str = Field(..., description="基于源场景摘要生成的、连贯的视觉描述。")
    selected_shot_order_ids: Optional[List[int]] = Field(
        None, description="由AI剪辑师在精剪阶段（阶段19）最终选定的、用于呈现此视觉节拍的镜头顺序ID列表。"
    )


# --- 微观场景分析 ---
class ShotAnalysisModel(BaseModel):
    shot_type: str = Field(..., description="景别 (从 特写, 近景, 中景, 全景, 远景 中选择)。")
    camera_angle: str = Field(..., description="机位角度 (从 平视, 俯视, 仰视 中选择)。")
    camera_movement: str = Field(..., description="运镜方式 (从 固定镜头, 推, 拉, 摇, 移, 跟, 升降 中选择)。")
    composition: str = Field(..., description="构图特点 (例如：三分法, 对称构图等)。")
    lighting: str = Field(..., description="光线风格 (例如：高调, 低调, 自然光等)。")


class SceneAnalysisResponse(BaseModel):
    visual_description: str = Field(
        ..., description="对整个镜头画面的综合性、叙事性、文学性的描述，总结核心视觉元素、氛围和潜在的象征意义。"
    )
    narrative_function: str = Field(
        ...,
        description="解读该镜头在叙事结构中的核心功能。例如：'建立镜头'、'角色引入'、'关键转折'、' foreshadowing(预示)'、'情绪锚点'等。",
    )
    aesthetic_analysis: str = Field(
        ...,
        description="对镜头美学风格的分析，包括色彩运用、光影情绪、构图的艺术表达等，解读其如何服务于影片的整体美学。",
    )
    people: str = Field(
        ..., description="识别画面中的主要人物，并深入描述他们的状态、微表情或肢体语言所揭示的内在信息。"
    )
    setting: str = Field(..., description="详细描述场景环境、地点和时间，并分析环境如何反映或影响角色的心境。")
    main_action: str = Field(..., description="描述该镜头内的核心动作或事件，并点出其对情节的推动作用。")
    emotion: str = Field(
        ...,
        description="分析并提炼该镜头所传达的核心情绪或情感基调，不仅识别表面情绪，更要推断其背后的复杂情感。",
    )
    key_objects: str = Field(..., description="列出与核心动作或具有叙事/象征意义的关键物体。")
    on_screen_text: str = Field(..., description="识别并转录画面中出现的任何重要文字。如果没有则返回空字符串。")
    shot_analysis: ShotAnalysisModel = Field(..., description="包含详细镜头语言技术参数分析的对象。")
    genre_specific_analysis: Optional[str] = Field(
        None, description="根据影片类型进行的特定分析。例如动作片的激烈程度、剧情片的潜台词、喜剧片的笑点类型等。"
    )


# --- 新增：D2S 场景镜头选择模型 ---
class ShotSelectionForSceneResponse(BaseModel):
    selected_shot_order_ids: List[int] = Field(
        ..., description="AI剪辑师最终决定使用的、按时间顺序排列的镜头 order_id 列表。"
    )
    justification: str = Field(..., description="AI做出这个剪辑选择的简要理由。")


# --- 电影解说文案 ---
class NarrationType(str, Enum):
    NARRATOR = "NARRATOR"
    INNER_MONOLOGUE = "INNER_MONOLOGUE"
    CHARACTER_DIALOGUE = "CHARACTER_DIALOGUE"


class ScriptBeat(BaseModel):
    beat_number: int = Field(..., description="叙事单元在整个剧本中的顺序编号，从1开始。")
    source_narrative_unit_number: int = Field(..., description="此剧本节拍对应的源叙事单元编号。")
    visual_beats: List[VisualBeat] = Field(
        ...,
        description="构成此叙事单元的、按时间顺序排列的纯视觉节拍列表。在阶段19后，此列表中的每个VisualBeat将包含`selected_shot_order_ids`字段。",
    )
    audio_content: Optional[str] = Field(
        None, description="对此整个叙事单元的综合旁白。在纯视觉剪辑阶段，此字段应为null。"
    )
    narration_type: Optional[NarrationType] = Field(
        None,
        description="音频的性质。在纯视觉剪辑阶段，此字段应为null。",
    )

    @model_validator(mode="after")
    def check_audio_logic(self) -> "ScriptBeat":
        if self.audio_content and self.narration_type is None:
            raise ValueError("如果提供了 'audio_content', 则必须指定 'narration_type'。")
        if self.narration_type and self.audio_content is None:
            raise ValueError("如果指定了 'narration_type', 则必须提供 'audio_content'。")
        return self


ScriptBeat.model_rebuild()  # 确保在所有模型定义完成后重建


class MovieCommentaryScriptResponse(BaseModel):
    script: List[Any] = Field(..., description="构成整个视频的音画节拍列表。")

    @model_validator(mode="after")
    def validate_script_beats(self):
        # 确保列表中的每个元素都是 ScriptBeat 类型
        from pydantic import ValidationError

        for i, item in enumerate(self.script):
            if not isinstance(item, ScriptBeat):
                try:
                    self.script[i] = ScriptBeat.model_validate(item)
                except ValidationError as e:
                    raise ValueError(f"Script item at index {i} is not a valid ScriptBeat: {e}")
        return self


# --- 音画映射 ---
# SentenceToSceneMapperResponse 不再需要，但为了兼容性保留


# --- 音频分析 ---
class AudioAnalysisResponse(BaseModel):
    transcript: str = Field(..., description="将所有可识别的对话转录为文字。")
    speaker_tone: str = Field(..., description="描述说话者的语气和情感。")
    music_analysis: str = Field(..., description="分析背景音乐的风格和营造的氛围。")
    key_sound_events: List[str] = Field(..., description="列出除对话和音乐外的关键声音事件。")


class ScriptEvaluationScore(BaseModel):
    score: int = Field(..., ge=1, le=5, description="评分 (1-5分)。")
    justification: str = Field(..., description="评分的详细理由。")


class ScriptEvaluationResponse(BaseModel):
    relevance: ScriptEvaluationScore = Field(
        ..., description="关联性 (Relevance): 剧本在多大程度上回应和实现了项目目标和主题？"
    )
    engagement: ScriptEvaluationScore = Field(
        ..., description="吸引力 (Engagement): 剧本是否拥有引人入胜的节奏、生动的对白和坚实的结构？"
    )
    adherence: ScriptEvaluationScore = Field(
        ..., description="遵循度 (Adherence): 角色行为和对白是否严格遵循了角色设定和动机？"
    )
    coherence: ScriptEvaluationScore = Field(
        ..., description="连贯性 (Coherence): 故事的情节发展是否符合逻辑？因果链条在剧本中是否得到了可信的呈现？"
    )
    technical_quality: ScriptEvaluationScore = Field(
        ...,
        description="技术性 (Technical-quality): 剧本格式是否规范？动作和声音描述是否清晰，足以指导后续的AI剪辑决策？",
    )


# --- 新增：镜头序列选择模型 ---
class ShotSequenceSelectionResponse(BaseModel):
    selected_shot_order_ids: List[int] = Field(..., description="AI选择的最佳镜头序列的顺序ID列表。")
    justification: str = Field(..., description="AI选择这个序列的理由。")


# --- 新增：标语生成模型 ---
class TaglineResponse(BaseModel):
    tagline: str = Field(..., description="为视频生成的一句简洁、有力、吸引人的宣传标语。")


# --- 新增：D2S 粗剪精炼响应模型 ---
class RoughCutRefinementResponse(BaseModel):
    selected_shot_order_ids: List[int] = Field(
        ..., description="AI剪辑师最终决定使用的、按时间顺序排列的镜头 order_id 列表。"
    )
    justification: str = Field(..., description="AI做出这个剪辑选择的简要理由。")


# --- 新增：D2S 单场景剧本评估响应模型 ---
class SceneScriptEvaluationResponse(BaseModel):
    score: int = Field(..., ge=1, le=5, description="对该场景剧本的总体评分 (1-5分)。")
    is_ready_for_production: bool = Field(..., description="判断该稿件是否已达到可用于生产的质量标准。")
    justification: str = Field(..., description="评分的简要理由。")
    suggested_improvements: str = Field(..., description="具体的、可操作的修改建议，用于指导下一轮修订。")


# ============== 智能剪辑优化相关模型 ==============


# --- 智能剪辑质量评估模型 ---
class DimensionScore(BaseModel):
    """单个维度的评分"""

    score: float = Field(..., ge=0.0, le=5.0, description="该维度的评分 (0-5分)")
    justification: str = Field(..., description="该维度评分的具体理由")


class IntelligentEditingEvaluationResponse(BaseModel):
    """智能剪辑质量评估响应"""

    overall_score: float = Field(..., ge=0.0, le=5.0, description="综合评分 (0-5分)")

    duration_control: DimensionScore = Field(..., description="时长控制维度评分")
    material_matching: DimensionScore = Field(..., description="素材匹配度维度评分")
    rhythm_coherence: DimensionScore = Field(..., description="节奏连贯性维度评分")
    visual_coherence: DimensionScore = Field(..., description="视觉连贯性维度评分")
    audio_video_sync: DimensionScore = Field(..., description="音画同步维度评分")

    critical_issues: List[str] = Field(..., description="识别出的致命问题列表")
    optimization_suggestions: List[str] = Field(..., description="优化建议列表")

    pass_threshold: float = Field(default=4.0, description="通过验证的最低分数阈值")
    is_acceptable: bool = Field(..., description="该剪辑方案是否可以接受")


# --- 节奏优化模型 ---
class RhythmAdjustment(BaseModel):
    """单个节拍的节奏调整方案"""

    beat_index: int = Field(..., description="节拍索引")
    original_duration: float = Field(..., description="原始时长(秒)")
    new_duration: float = Field(..., description="调整后时长(秒)")
    adjustment_reason: str = Field(..., description="调整理由")


class RhythmOptimizationResponse(BaseModel):
    """节奏优化响应"""

    rhythm_score: float = Field(..., ge=0.0, le=5.0, description="当前节奏质量评分")
    needs_optimization: bool = Field(..., description="是否需要进行节奏优化")

    target_avg_duration: float = Field(..., description="目标平均镜头时长(秒)")
    target_variance: float = Field(..., description="目标时长方差")

    adjustments: List[RhythmAdjustment] = Field(..., description="具体的节奏调整方案")

    optimization_summary: str = Field(..., description="优化方案的总体说明")
    expected_improvement: str = Field(..., description="预期的改进效果")


# --- 时长压缩模型 ---
class BeatPriority(BaseModel):
    """节拍优先级评估"""

    beat_index: int = Field(..., description="节拍索引")
    priority_score: float = Field(..., ge=0.0, le=1.0, description="优先级分数 (0-1)")
    content_type: str = Field(..., description="内容类型 (核心情节/过渡/细节描述)")
    keep_decision: str = Field(..., description="保留决定 (保留/压缩/删除)")
    justification: str = Field(..., description="决定理由")


class DurationCompressionResponse(BaseModel):
    """时长压缩响应"""

    original_duration: float = Field(..., description="原始总时长(秒)")
    target_duration: float = Field(..., description="目标时长(秒)")
    final_duration: float = Field(..., description="压缩后时长(秒)")
    compression_ratio: float = Field(..., description="实际压缩比例")

    beat_priorities: List[BeatPriority] = Field(..., description="各节拍的优先级评估")

    compression_strategy: str = Field(..., description="采用的压缩策略描述")
    quality_preservation_notes: str = Field(..., description="质量保持说明")

    removed_beats_count: int = Field(..., description="删除的节拍数量")
    compressed_beats_count: int = Field(..., description="压缩的节拍数量")


# --- 剪辑审查模型 ---
class EvaluationReview(BaseModel):
    """对评估结果的审查"""

    dimension_name: str = Field(..., description="被审查的维度名称")
    original_score: float = Field(..., description="原始评分")
    is_reasonable: bool = Field(..., description="评分是否合理")
    suggested_score: Optional[float] = Field(None, description="建议的修正评分")
    review_reason: str = Field(..., description="审查意见")


class EditingReviewResponse(BaseModel):
    """剪辑审查响应"""

    overall_evaluation_is_reasonable: bool = Field(..., description="整体评估是否合理")

    dimension_reviews: List[EvaluationReview] = Field(..., description="各维度评估的审查结果")

    critical_issues_review: str = Field(..., description="对致命问题分类的审查意见")
    suggestions_review: str = Field(..., description="对优化建议的审查意见")

    evaluation_consistency_score: float = Field(..., ge=0.0, le=5.0, description="评估一致性得分")

    final_recommendation: str = Field(..., description="最终建议 (接受评估/重新评估/人工审核)")
    review_summary: str = Field(..., description="审查总结")


# --- 智能镜头选择模型 ---
class ShotSelectionCandidate(BaseModel):
    """候选镜头评估"""

    shot_order_id: int = Field(..., description="镜头顺序ID")
    content_match_score: float = Field(..., ge=0.0, le=1.0, description="内容匹配分数")
    emotion_match_score: float = Field(..., ge=0.0, le=1.0, description="情感匹配分数")
    duration_match_score: float = Field(..., ge=0.0, le=1.0, description="时长匹配分数")
    overall_score: float = Field(..., ge=0.0, le=1.0, description="综合评分")
    selection_reason: str = Field(..., description="选择或排除的理由")


class IntelligentShotSelectionResponse(BaseModel):
    """智能镜头选择响应"""

    selected_shot_order_ids: List[int] = Field(..., description="最终选择的镜头ID列表")

    candidate_evaluations: List[ShotSelectionCandidate] = Field(..., description="所有候选镜头的评估结果")

    total_selected_duration: float = Field(..., description="选择镜头的总时长(秒)")
    target_duration: float = Field(..., description="目标时长(秒)")
    duration_match_ratio: float = Field(..., description="时长匹配比例")

    selection_strategy: str = Field(..., description="选择策略说明")
    overall_confidence: float = Field(..., ge=0.0, le=1.0, description="整体置信度")

    fallback_applied: bool = Field(default=False, description="是否应用了降级备选方案")
    fallback_reason: Optional[str] = Field(None, description="降级原因")

    justification: str = Field(..., description="最终选择的综合理由")

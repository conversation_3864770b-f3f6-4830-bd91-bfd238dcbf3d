"""
故事大纲生成模块 (Stage 14)
"""
from typing import List

from pydantic import BaseModel, Field

from .base import solver_prompt


# ============= 模型定义 =============
class StoryOutlineItem(BaseModel):
    narrative_unit_number: int = Field(..., description="叙事单元的编号，从1开始。")
    narrative_goal: str = Field(..., description="该叙事单元在故事中的核心目标或作用。")
    characters_present: List[str] = Field(..., description="在此场景中出现的角色的character_id列表。")
    setting: str = Field(..., description="场景发生的地点和时间。")
    candidate_scene_ids: List[int] = Field(..., description="可用于视觉化呈现该场景的一组候选源场景的数字ID列表。")
    summary: str = Field(
        ...,
        description="对该场景内容的详细段落描述。请详细阐述场景的起因、经过、结果，以及关键角色的行动和反应，确保每个场景的描述都足够丰富，能够独立成段。",
    )


class StoryOutlineResponse(BaseModel):
    story_outline: List[StoryOutlineItem]


# ============= 提示词定义 =============
OUTLINE_GENERATION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位结构感极强的总编剧。你的任务是根据以下经过验证的因果图谱、项目信息、角色设定以及外部研究资料，生成一份详细的、分场景的故事大纲。

# Input: One-Sentence Summary (Tagline)
---
{tagline}
---

# Input: Research Summary
---
{research_summary}
---

# Input: Causal Plot Graph
---
{causal_graph_json}
---

# Input: Project Info & Character Profiles
---
### 项目信息 (Project Info)
{project_info_json}

### 角色设定 (Characters)
{characters_json}

### 叙事视角: {narration_perspective}
---

# Task & Rules
1.  **遍历图谱**: 请使用时间顺序来组织叙事单元的顺序，遍历整个图谱。
2.  **单元定义**: 图中的每一个节点（或一组紧密关联的节点）都应被转换成大纲中的一个叙事单元。
3.  **【核心要求】详细描述**: 对于每个叙事单元的 `summary` 字段，你必须撰写一个详细的段落（约100-150字）。这个段落需要清晰地描述单元的起因、核心事件、关键转折和结局，以及主要角色的行动和反应。目标是让每个单元的描述都足够丰富，能够独立成章。
4.  **角色关联**: 【重要】在填充每个单元的 `characters_present` 字段时，你必须使用**因果图谱节点中已提供的角色名字**，并从“角色设定”中找到对应的 `character_id` 进行填充。
5.  **【核心要求】源场景ID**: 在填充 `candidate_scene_ids` 字段时，你必须从输入事件的 `source_scene_ids` 字段中提取**纯数字ID**，并以整数列表的形式提供，例如 `[1, 2, 3]`。
6.  **保持原文名称**: 在 `summary` 和 `characters_present` 字段中引用角色时，必须使用他们在“角色设定”中的原始名称，不要翻译。
7.  **核对事实**: 在创作过程中，请务必核对研究资料中的事实，确保信息准确无误。
8.  **详细描述要求**: 为每个叙事单元指定编号、目标、涉及角色、设定和候选源场景ID。

请开始你的大纲生成工作。
""")

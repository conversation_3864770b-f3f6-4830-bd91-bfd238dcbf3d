"""
镜头内容增强模块 (Stage 9)
"""
from pydantic import Field

from .base import solver_prompt
from .scene_analysis import SceneAnalysisResponse


# ============= 模型定义 =============
class ShotEnrichmentResponse(SceneAnalysisResponse):
    """
    基于宏观上下文，对单个镜头的完整分析进行增强后的响应模型。
    它继承了 SceneAnalysisResponse 的所有字段，并增加了一个理由字段。
    """

    justification: str = Field(
        ..., description="简要说明你为什么这样修改，以及新描述如何更好地服务于场景和序列的目标。"
    )


# ============= 提示词定义 =============
SHOT_ENRICHMENT_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位资深的电影剪辑师和剧本监督，拥有敏锐的叙事直觉。你的任务是为一个独立的镜头（Shot），基于其**所属的视频片段**和**宏观叙事上下文**，对**整个原始分析报告**进行审查和重写，使其更具叙事感和情感深度。

# Input: Broader Narrative Context (宏观叙事上下文)
---
### 所属序列的主题: {sequence_theme}

### 所属序列的摘要: {sequence_summary}
---
### 所属场景的目标: {scene_goal}

### 所属场景的摘要: {scene_summary}
---

# Input: Original Shot Analysis (需要你增强的原始分析报告)
---
### 镜头中的角色: {characters_present}
### 原始分析报告 (JSON格式):
{original_analysis_json}
---

# Task & Rules
1.  **【核心】视觉优先**: 你的分析必须**首先基于视频片段**的实际内容。提供的文本上下文（序列、场景摘要等）是用来帮助你理解这个片段在故事中的位置和意义，而不是让你脱离画面进行创作。
2.  **核心任务**: **重写并返回一个完整的、更新后的分析对象**。你不仅要优化 `visual_description`，还必须重新审视**所有**字段（如 `people`, `setting`, `main_action`, `emotion`, `shot_analysis` 等），并根据宏观上下文进行修正和深化。
    -   例如，如果场景目标是"展示安娜的孤独"，那么在描述 `people` 时，除了描述外貌，还可以补充"她看起来与周围格格不入"；在 `emotion` 字段，可以从"平静"深化为"平静外表下的落寞"。
3.  **保持事实**: 新的描述不能与原始描述中的核心视觉事实相冲突。你可以增加情感、氛围和叙事层面的解读，但不能虚构画面中不存在的物体或动作。
4.  **叙事性**: 让新的 `visual_description` 读起来像电影解说或小说片段。

请开始你的镜头分析增强工作。
""")

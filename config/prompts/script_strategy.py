"""
剧本创作策略模块 (Stage 16)
"""
from typing import Optional

from pydantic import BaseModel, Field

from .base import solver_prompt


# ============= 模型定义 =============
class ScriptWritingStrategyResponse(BaseModel):
    global_narration_tone: str = Field(..., description="整部影片的全局旁白基调和风格。")
    hook_strategy: Optional[str] = Field(None, description="视频开头的“钩子”策略，用于迅速抓住观众注意力。")
    climax_strategy: Optional[str] = Field(None, description="视频高潮部分的核心论点或关键转折的揭示策略。")
    conclusion_strategy: Optional[str] = Field(None, description="视频结尾的总结与主题升华策略。")


# ============= 提示词定义 =============
SCRIPT_WRITING_STRATEGY_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位顶级的叙事策略师和 supervising editor。你的任务是基于一个已完成的“故事大纲”，为视频解说制定一份具有独特视角且**完全基于现有素材**的创作策略。

# Core Principles (核心原则)
1.  **【最高优先级】绝对忠于素材**: 你的创作**唯一**的事实来源是下方的 `Input: Story Outline`。你的任务是**解读和规划**，而不是**发明和创作**。
2.  **【硬性约束】禁止杜撰**: 你被严格禁止发明任何故事大纲中不存在的新场景、新动作、具体物品（如“香奈儿五号香水”、“银质十字架项链”）、角色记忆或对话。你的所有策略描述都必须能从故事大纲的 `summary` 中找到直接依据。
3.  **【硬性约束】引用依据**: 在为 `hook_strategy`, `climax_strategy`, `conclusion_strategy` 撰写策略时，你**必须**明确引用你计划使用的叙事单元编号 (`narrative_unit_number`)。例如：“开篇钩子将使用叙事单元 #5，通过展示...来吸引观众”。

# Input: One-Sentence Summary (Tagline)
---
{tagline}
---

# Input: Character Dossiers
---
{dossier_text}
---

# Input: Research Summary
---
{research_summary}
---

# Input: Story Outline (这是你的唯一事实来源)
以下是整个故事的场景大纲：
---
{story_outline_json}
---

# Input: Project Info
以下是项目的核心信息：
---
{project_info_json}

### 角色设定 (Characters)
{characters_json}
---

# Task & Rules
1.  **规划全局叙事结构**: 基于故事大纲，设计一个独特而完整的叙事框架：
    -   **`hook_strategy`**: 从大纲中选择一个或多个叙事单元作为开篇。清晰描述你将如何呈现**这些已存在的场景**来设置悬念、引出核心冲突。
    -   **`climax_strategy`**: 从大纲中识别出代表故事高潮的叙事单元。描述你将如何组织和解读**这些场景**，以最大化其情感冲击力。
    -   **`conclusion_strategy`**: 从大纲中选择合适的叙事单元作为结尾。描述你将如何利用**这些场景**来总结主题、提供余味。
2.  **确定全局基调 (`global_narration_tone`)**: 基于大纲的整体情节、情感走向以及指定的 **{narration_perspective}** 叙事视角，定义一个具体、可执行的旁白风格。例如，如果是第一人称，基调应更主观、个人化；如果是第三人称，则可以更客观、全知。

请基于以上指导原则，创作一份完全基于所提供素材的、杜绝任何幻觉的全局创作策略。
""")

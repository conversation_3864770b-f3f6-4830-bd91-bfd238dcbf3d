"""
场景分组模块 (Stage 7)
包含镜头聚合为场景的提示词和对应的 Pydantic 模型
"""

from enum import Enum
from typing import List
from pydantic import BaseModel, Field
from .base import solver_prompt, verifier_prompt


# ============= 模型定义 =============
class SceneGroupingItem(BaseModel):
    start_shot_id: int = Field(..., description="组成这个场景的第一个镜头的ID。")
    end_shot_id: int = Field(..., description="组成这个场景的最后一个镜头的ID。")
    summary: str = Field(..., description="对这个场景的整体内容进行简洁的叙事性总结。")
    narrative_purpose: str = Field(..., description="总结这个场景在故事中的作用。")


class SceneGroupingResponse(BaseModel):
    scenes: List[SceneGroupingItem]


class OrphanFixDecisionEnum(str, Enum):
    PRECEDING = "preceding"
    SUCCEEDING = "succeeding"
    NEW_SCENE = "new_scene"


class OrphanFixDecision(BaseModel):
    decision: OrphanFixDecisionEnum = Field(
        ...,
        description="你的决策。必须是 'preceding', 'succeeding', 或 'new_scene' 中的一个。",
    )


# ============= 提示词定义 =============
SCENE_GROUPING_PROMPT = solver_prompt(r"""
你是一位经验丰富的剪辑师。你的任务是将以下按时间顺序排列的镜头(Shot)描述，划分成几个具有连贯叙事逻辑的场景(Scene)。一个场景通常发生在同一时间、同一地点，或者在剧情上有密切的联系或连续性。

**特别注意：如果连续的几个镜头拥有完全相同的对白，这极大概率意味着它们属于同一个对话场景，应该被分在同一组。**

镜头列表:
{shot_descriptions}

请分析镜头列表，识别出场景的边界，并使用提供的工具来构建你的分析结果。
对于每个场景，你必须指定 `start_shot_id` 和 `end_shot_id` 来定义其包含的连续镜头范围。这两个ID都必须是"镜头列表"中存在的ID。
""")


ORPHAN_SHOT_FIX_PROMPT = verifier_prompt(r"""
# Role & Goal
你是一位顶级的电影剪辑师，正在进行剪辑审查。你发现了一个在初步剪辑中被遗漏的"孤儿镜头"。你的任务是根据其前后的场景内容，决定这个孤儿镜头最合理的归属。

# Context
- **前一个场景 (Preceding Scene)**: 包含一个或多个镜头，构成一个连贯的叙事单元。
- **孤儿镜头 (Orphan Shot)**: 这是那个被遗漏的、需要被重新安置的镜头。
- **后一个场景 (Succeeding Scene)**: 紧跟在孤儿镜头之后的另一个连贯的叙事单元。

# Input Data
---
### 前一个场景的镜头描述:
{preceding_scene_shots}
---
### **【需要你决策的孤儿镜头】**:
{orphan_shot}
---
### 后一个场景的镜头描述:
{succeeding_scene_shots}
---

# Task
请仔细分析孤儿镜头的内容，并与前后两个场景进行比较。然后做出以下三种决策之一：
1.  `preceding`: 如果孤儿镜头在叙事、时空、或内容上是前一个场景的延续，则应归属于前一个场景。
2.  `succeeding`: 如果孤儿镜头更像是后一个场景的开端，则应归属于后一个场景。
3.  `new_scene`: 如果孤儿镜头与前后场景都无明显关联，内容独立，则它应该自成一个新的单镜头场景。

请使用提供的工具返回你的决策。
""")
"""
提示词模块包
将各种AI提示词按功能分组到独立模块中
"""

from .audio_analysis import AUDIO_ANALYSIS_PROMPT, AudioAnalysisResponse
from .character_dossier import (
    CHARACTER_DOSSIER_GENERATION_PROMPT,
    CHARACTER_DOSSIER_REFINE_PROMPT,
    CharacterDossierItem,
    CharacterDossierResponse,
)
from .character_labeling import CHARACTER_AI_LABELING_PROMPT, AILabelingResponse
from .d2s_reader import (
    CAUSAL_LINK_INFERENCE_PROMPT,
    EVENT_IDENTIFICATION_PROMPT,
    CausalLink,
    CausalLinkInferenceResponse,
    EventIdentificationResponse,
    NarrativeEvent,
)
from .duration_control import BEAT_TRIM_PROMPT, TrimmedBeatResponse
from .editing import (
    ROUGH_CUT_REFINEMENT_PROMPT,
    SHOT_SELECTION_FOR_SCENE_PROMPT,
    RoughCutRefinementResponse,
    ShotSelectionForSceneResponse,
)
from .foundation import (
    CHARACTER_COMPLETION_PROMPT,
    CREATIVE_BRIEF_GENERATION_PROMPT,
    RELATIONSHIP_ENRICHMENT_PROMPT,
    CharacterCompletionResponse,
    DesignDocParseResponse,
)
from .outline_generation import (
    OUTLINE_GENERATION_PROMPT,
    StoryOutlineItem,
    StoryOutlineResponse,
)
from .research import (
    RESEARCH_PLAN_PROMPT,
    RESEARCH_SUMMARY_PROMPT,
    ResearchPlanResponse,
)
from .scene_analysis import GENRE_INSTRUCTIONS, SCENE_ANALYSIS_PROMPT, SceneAnalysisResponse, ShotAnalysisModel
from .scene_grouping import (
    ORPHAN_SHOT_FIX_PROMPT,
    SCENE_GROUPING_PROMPT,
    OrphanFixDecision,
    SceneGroupingItem,
    SceneGroupingResponse,
)
from .script_strategy import SCRIPT_WRITING_STRATEGY_PROMPT, ScriptWritingStrategyResponse
from .script_writing import (
    NARRATION_INTERVENTION_INSTRUCTIONS,
    SCENE_SCRIPT_EVALUATION_PROMPT,
    SCENE_SCRIPT_SELF_CORRECTION_PROMPT,
    SCRIPT_EVALUATION_PROMPT,
    VISUAL_BEAT_ASSEMBLY_PROMPT,
    MovieCommentaryScriptResponse,
    SceneScriptEvaluationResponse,
    ScriptBeat,
    ScriptEvaluationResponse,
    VisualBeat,
)
from .sequence_analysis import (
    CHARACTER_EMOTIONAL_ARC_PROMPT,
    SEQUENCE_ANALYSIS_PROMPT,
    CharacterEmotionalArc,
    CharacterEmotionalArcResponse,
    SequenceAnalysisItem,
    SequenceAnalysisResponse,
)
from .shot_enrichment import SHOT_ENRICHMENT_PROMPT, ShotEnrichmentResponse
from .story_flow import (
    STORY_FLOW_ARRANGEMENT_PROMPT,
    StoryFlowItem,
    StoryFlowResponse,
)
from .tagline import TAGLINE_GENERATION_PROMPT, TaglineResponse

__all__ = [
    # Tagline 模块
    "TAGLINE_GENERATION_PROMPT",
    "TaglineResponse",
    # Scene Analysis 模块
    "SCENE_ANALYSIS_PROMPT",
    "SceneAnalysisResponse",
    "ShotAnalysisModel",
    "GENRE_INSTRUCTIONS",
    # Scene Grouping 模块
    "ORPHAN_SHOT_FIX_PROMPT",
    "OrphanFixDecision",
    "SCENE_GROUPING_PROMPT",
    "SceneGroupingItem",
    "SceneGroupingResponse",
    # Character Dossier 模块
    "CHARACTER_DOSSIER_GENERATION_PROMPT",
    "CHARACTER_DOSSIER_REFINE_PROMPT",
    "CharacterDossierItem",
    "CharacterDossierResponse",
    # Script Strategy 模块
    "SCRIPT_WRITING_STRATEGY_PROMPT",
    "ScriptWritingStrategyResponse",
    # Research 模块
    "RESEARCH_PLAN_PROMPT",
    "RESEARCH_SUMMARY_PROMPT",
    "ResearchPlanResponse",
    # Sequence Analysis 模块
    "SEQUENCE_ANALYSIS_PROMPT",
    "SequenceAnalysisResponse",
    "SequenceAnalysisItem",
    "CHARACTER_EMOTIONAL_ARC_PROMPT",
    "CharacterEmotionalArcResponse",
    "CharacterEmotionalArc",
    # Shot Enrichment 模块
    "SHOT_ENRICHMENT_PROMPT",
    "ShotEnrichmentResponse",
    # D2S Reader 模块
    "EVENT_IDENTIFICATION_PROMPT",
    "EventIdentificationResponse",
    "NarrativeEvent",
    "CAUSAL_LINK_INFERENCE_PROMPT",
    "CausalLinkInferenceResponse",
    "CausalLink",
    # Editing 模块
    "ROUGH_CUT_REFINEMENT_PROMPT",
    "RoughCutRefinementResponse",
    "SHOT_SELECTION_FOR_SCENE_PROMPT",
    "ShotSelectionForSceneResponse",
    # Foundation 模块
    "CREATIVE_BRIEF_GENERATION_PROMPT",
    "CHARACTER_COMPLETION_PROMPT",
    "RELATIONSHIP_ENRICHMENT_PROMPT",
    "DesignDocParseResponse",
    "CharacterCompletionResponse",
    # Outline Generation 模块
    "OUTLINE_GENERATION_PROMPT",
    "StoryOutlineItem",
    "StoryOutlineResponse",
    # Story Flow 模块
    "STORY_FLOW_ARRANGEMENT_PROMPT",
    "StoryFlowItem",
    "StoryFlowResponse",
    # Duration Control 模块
    "BEAT_TRIM_PROMPT",
    "TrimmedBeatResponse",
    # Audio Analysis 模块
    "AUDIO_ANALYSIS_PROMPT",
    "AudioAnalysisResponse",
    # Script Writing 模块
    "NARRATION_INTERVENTION_INSTRUCTIONS",
    "VISUAL_BEAT_ASSEMBLY_PROMPT",
    "SCRIPT_EVALUATION_PROMPT",
    "SCENE_SCRIPT_EVALUATION_PROMPT",
    "SCENE_SCRIPT_SELF_CORRECTION_PROMPT",
    "MovieCommentaryScriptResponse",
    "ScriptEvaluationResponse",
    "SceneScriptEvaluationResponse",
    "ScriptBeat",
    "VisualBeat",
    # AI 角色自动标注模块
    "AILabelingResponse",
    "CHARACTER_AI_LABELING_PROMPT",
]

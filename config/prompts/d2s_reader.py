"""
D2S 读者模块提示词 (Stages 11, 12)
包含事件识别、因果推断的提示词和模型
"""
from typing import List, Optional

from pydantic import BaseModel, Field

from .base import solver_prompt

# ============= 模型定义 =============

# --- D2S Reader: 事件识别模型 (Stage 11) ---
class NarrativeEvent(BaseModel):
    event_id: Optional[str] = Field(None, description="事件的唯一ID，由系统在后处理中分配，格式为 'event_XXX'。")
    event_description: str = Field(..., description="对这个叙事事件的简洁、高度概括的描述。")
    importance_score: float = Field(
        ..., ge=0.0, le=1.0, description="该事件的叙事重要性评分 (0.0-1.0)，用于过滤次要事件。"
    )
    psychological_motivation: str = Field(..., description="驱动此事件发生的核心角色的主要心理动机或内在状态。")
    characters_present: List[str] = Field(..., description="在此事件中出现的角色的名字列表。")
    source_scene_numbers: List[int] = Field(..., description="构成这个事件的一个或多个源场景的数字编号列表。")

class EventIdentificationResponse(BaseModel):
    narrative_events: List[NarrativeEvent]

# --- D2S Reader: 因果链接推断模型 (Stage 12) ---
class CausalLink(BaseModel):
    source_event_id: str = Field(..., description="因果关系中的源头事件ID。")
    target_event_id: str = Field(..., description="因果关系中的目标事件ID。")
    causality_description: str = Field(..., description="对这条因果关系的简要描述和推理依据。")

class CausalLinkInferenceResponse(BaseModel):
    causal_links: List[CausalLink]

# ============= 提示词定义 =============

# --- D2S Reader: 事件识别提示词 (Stage 11) ---
EVENT_IDENTIFICATION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位专业的叙事理论家和剧本医生。你的任务是仔细分析以下按时间顺序排列的**场景摘要列表**，并从中识别出推动故事发展的关键“叙事事件”，并评估其重要性。

一个“叙事事件”是一个有意义的、能够推动情节发展的动作或时刻。它可能由一个或多个连续的场景构成。

# Input: Scene Data
---
{scene_data_json}
---

# Extraction Principles (提取原则)
按出现顺序提取角色、情感和关系。  使用精确文本进行提取，不要改写或重叠实体。  为每个实体提供有意义的属性以添加上下文。

# Task & Rules
1.  **输入理解**: 你的输入是一个JSON数组，其中每个对象代表一个按时间顺序排列的场景，包含`scene_number`, `summary`等关键信息。
2.  **事件粒度**: 每个事件描述必须只包含**一个**核心动作/情节节点。
    - 例如 “Anna 在巴黎开始模特工作” 与 “被介绍给 Oleg” 是两件独立的剧情，应拆分成两个 event。
    - 如果一个场景里出现数个时间、地点或目标明显不同的动作，请分别生成多条事件。
3.  **识别事件**: 从提供的场景摘要中识别出所有关键的叙事事件。
4.  **【核心新增】评估重要性**: 为每个事件评估其`importance_score` (0.0-1.0)。评分标准：
    -   **高分 (0.8-1.0)**: 关键情节转折、核心冲突、角色命运的重大改变。
    -   **中分 (0.4-0.7)**: 推动情节发展的重要步骤、关键信息的揭示、角色关系的重要变化。
    -   **低分 (0.0-0.3)**: 背景铺垫、氛围营造、次要情节。
5.  **【核心新增】分析动机**: 为每个事件分析并填写`psychological_motivation`字段，描述驱动事件发生的核心角色的内在心理状态或动机。
6.  **简洁描述**: 为每个事件提供一个简洁但信息丰富的描述。
7.  **提取角色**: 【重要】在 `characters_present` 字段中，你必须准确地列出在该事件涉及的场景中出现的所有角色名称。
8.  **关联素材**: 【硬性约束】在`source_scene_numbers`字段中，你必须从输入数据中找到构成该事件的源场景，并直接使用它们的 `scene_number` **数字**。例如，如果一个事件由场景2和场景3构成，则此字段应为 `[2, 3]`。

请开始你的分析和提取工作。
""")

# --- D2S Reader: 因果链接推断提示词 (Stage 12) ---
CAUSAL_LINK_INFERENCE_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位擅长因果逻辑与深层心理分析的叙事理论家。你的任务是基于以下已识别的事件列表，并参考项目的全局设定（项目信息、角色档案），推断这些事件之间的因果关系，特别是要挖掘行为背后的心理动机。

# Input: Research Summary
---
{research_summary}
---

# Input: Project Info & Character Dossiers
---
### 项目信息 (Project Info)
{project_info_json}

### 角色档案 (Character Dossiers)
{dossier_text}
---

# Input: Narrative Events
以下是已识别的事件列表，请特别注意每个事件附带的`psychological_motivation`字段，它为你提供了分析角色行为的直接线索。
---
{narrative_events_json}
---

# Task & Rules
1.  **推断链接**: 仔细分析每个事件，并找出它们之间所有可能的、合乎逻辑的因果关系。
2.  **【核心】深层因果分析**: 在撰写`causality_description`时，你必须超越表面的“行动-结果”关系。请结合角色档案和事件的心理动机，解释**为什么**一个事件会导致另一个事件的发生。例如，不要只说“A的死亡导致B的复仇”，而要说“由于B对A深厚的忠诚（来自角色档案），A的死亡触发了B强烈的保护欲和复仇动机，从而导致了他的复仇行动。”
3.  **全面覆盖**: 尝试为尽可能多的事件建立联系，但不要凭空捏造。

请开始你的因果推断工作。
""")

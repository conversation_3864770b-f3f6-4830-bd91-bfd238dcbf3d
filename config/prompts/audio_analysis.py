"""
音频分析模块
包含音频分析的提示词和对应的 Pydantic 模型
"""

from pydantic import BaseModel, Field
from .base import solver_prompt

# ============= 模型定义 =============
class AudioAnalysisResponse(BaseModel):
    speech_content: str = Field(..., description="转录的可辨识语音内容。如果没有则返回空字符串。")
    speaker_analysis: str = Field(..., description="对说话者音色、语速、语调的分析，以及对其情绪和意图的判断。")
    music_analysis: str = Field(..., description="对背景音乐的风格、乐器、节奏及其氛围贡献的分析。如果没有则返回'无'。")
    sfx_analysis: str = Field(..., description="对关键音效及其作用的识别和解释。如果没有则返回'无'。")
    overall_mood: str = Field(..., description="对整个音频片段传达的核心情绪或氛围的综合性总结。")

# ============= 提示词定义 =============
AUDIO_ANALYSIS_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位资深的音频工程师和声音设计师。你的任务是针对所提供的音频片段，进行一次深入的、结构化的听觉分析。请将你的分析聚焦于音频元素如何传递信息、塑造情感和营造氛围。

# Input
---
### 音频片段
你将通过多模态接口接收一个音频片段。

# Task & Deconstruction Framework (任务与解构框架)
你的核心任务是**逐项解构**音频的构成元素，并解读其内在的叙事功能与情感价值。你的分析应当精准、专业，并严格按照提供的工具/格式进行输出。

**提取原则**: 按出现顺序提取角色、情感和关系。  使用精确文本进行提取，不要改写或重叠实体。  为每个实体提供有意义的属性以添加上下文。

具体要求如下：

-   **`speech_content`**: 如果有可辨识的人声对话，请完整转录。如果没有，明确指出“无人声”。
-   **`speaker_analysis`**: 如果有语音，请分析说话者的音色、语速、语调，并推断其情绪状态和潜在意图。
-   **`music_analysis`**: 识别背景音乐。描述其风格（如：管弦、电子、爵士）、所用主要乐器、节奏快慢，并分析它如何营造或增强场景的氛围。
-   **`sfx_analysis`**: 识别出关键的、具有叙事意义的环境音或音效（如：关门声、风声、爆炸声），并解释其功能。
-   **`overall_mood`**: 综合以上所有元素，用几个关键词提炼并总结该音频片段所传达的整体情绪基调和氛围（如：紧张悬疑、轻松愉快、悲伤沉重）。
""")

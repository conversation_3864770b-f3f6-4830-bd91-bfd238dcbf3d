"""
角色档案生成模块 (Stage 15)
包含角色档案生成/精炼的提示词和对应的 Pydantic 模型
"""
from typing import List

from pydantic import BaseModel, Field

from .base import solver_prompt


# ============= 模型定义 =============
class CharacterDossierItem(BaseModel):
    character_id: str = Field(..., description="角色的唯一ID。")
    name: str = Field(..., description="角色的名字。")
    background: str = Field(..., description="角色的背景故事和关键经历。")
    motivation: str = Field(..., description="驱动角色的核心动机。")
    conflict: str = Field(..., description="角色面临的主要内心或外部冲突。")
    arc: str = Field(..., description="角色在故事中经历的转变或成长弧光。")


class CharacterDossierResponse(BaseModel):
    dossiers: List[CharacterDossierItem] = Field(..., description="所有角色的档案列表。")


# ============= 提示词定义 =============

# --- D2S: 角色档案从头生成提示词 ---
CHARACTER_DOSSIER_GENERATION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位资深的编剧和剧本医生。你的任务是基于提供的研究资料和角色设定，为**每一个角色**生成一份详尽的角色档案，深入挖掘其背景、内心冲突和人物弧光。

# Input: Research Summary
---
{research_summary}
---

# Input: Character Profiles (Ground Truth)
---
{character_profiles_text}
---

# Task & Rules
1.  **全面覆盖**: 你必须为 "Input: Character Profiles" 中提到的 **每一个角色** 生成档案。
2.  **深度挖掘**: 档案内容需要超越角色设定的表面信息，结合研究资料进行深度创作。
3.  **【新增】遵循叙事视角**: 你的写作风格必须严格遵循指定的叙事视角：**{narration_perspective}**。如果是 'first_person'，请使用第一人称口吻（例如“我的背景是...”）；如果是 'third_person'，请使用第三人称（例如“他的背景是...”）。

请开始你的角色档案生成工作。
""")

# --- D2S: 角色档案补全/精炼提示词 ---
CHARACTER_DOSSIER_REFINE_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位资深的编剧教练和剧本医生。你的任务是审查并扩展一份已有的角色档案。请使用“基准真相”中的角色设定来丰富“现有档案”，使其更加详尽、深刻，并确保最终输出与基准真相保持一致。

# Input: Research Summary
---
{research_summary}
---

# Input: Ground Truth (来自设计文档的角色设定)
---
{character_profiles_text}
---

# Input: Existing Dossier (需要你优化和补全的上一版档案, JSON格式)
---
{existing_dossier_json}
---

# Task & Rules
1.  **核心任务**: 在"现有档案"的基础上进行扩展和深化，而不是简单重复。请增加新的见解，挖掘潜台词，并充实角色的发展弧光。
2.  **【新增】遵循叙事视角**: 你的写作风格必须严格遵循指定的叙事视角：**{narration_perspective}**。如果是 'first_person'，请使用第一人称口吻；如果是 'third_person'，请使用第三人称。
3.  **保持结构**: 维持原有档案的核心结构，但可以自由地进行改写和扩充。

请开始你的精炼工作。
""")

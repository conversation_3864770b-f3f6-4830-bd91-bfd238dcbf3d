"""
剪辑模块提示词 (Stages 19, 21)
包含 AI 精剪、粗剪精炼等相关提示词和模型
"""
from typing import List

from pydantic import BaseModel, Field

from .base import solver_prompt


# ============= 模型定义 =============

# --- D2S 场景镜头选择模型 (Stage 19) ---
class ShotSelectionForSceneResponse(BaseModel):
    selected_shot_order_ids: List[int] = Field(
        ..., description="AI剪辑师最终决定使用的、按时间顺序排列的镜头 order_id 列表。"
    )
    justification: str = Field(..., description="AI做出这个剪辑选择的简要理由。")


# --- D2S 粗剪精炼响应模型 (Stage 21) ---
class RoughCutRefinementResponse(BaseModel):
    selected_shot_order_ids: List[int] = Field(
        ..., description="AI剪辑师最终决定使用的、按时间顺序排列的镜头 order_id 列表。"
    )
    justification: str = Field(..., description="AI做出这个剪辑选择的简要理由。")


# ============= 提示词定义 =============

# --- D2S Rewriter: 场景镜头选择提示词 (Stage 19) ---
SHOT_SELECTION_FOR_SCENE_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位顶级的电影剪辑师，拥有卓越的镜头感和叙事直觉。你的任务是为一个场景的“视觉剧本”（Visual Beat），从一系列可用的候选镜头中，挑选出一个最能表达其内容的镜头序列。

# Input: Visual Beat Script (视觉剧本)
这是本场景需要用镜头来呈现的核心视觉叙事：
---
{visual_beat_description}
---

# Input: Available Shots (本场景所有可用的候选镜头)
以下是本场景所有可用的候选镜头，已按时间顺序排列：
---
{candidate_shots_json}
---

# Task & Rules
1.  **核心任务**: 从“可用镜头”列表中，挑选出一个子序列。这个子序列必须在视觉上与“视觉剧本”高度匹配。
2.  **叙事连贯**: 确保你选择的镜头序列在视觉上是连贯流畅的，能够清晰地讲述一个小故事。
3.  **【硬性约束】动作完整性 (Action Integrity)**: 你必须保留完整的动作链条。如果一个镜头是某个动作的结果（例如：开枪、关门、倒下），那么你必须也选择展示该动作起因或准备过程的前置镜头。绝对不能只选择高潮瞬间而省略其必要的铺垫镜头。
4.  **保留精华**: 在满足“动作完整性”的前提下，优先选择那些最具信息量、情感最饱满的镜头。你可以省略一些重复或不重要的过渡镜头。
5.  **顺序要求**: 你返回的 `shot_order_id` 列表必须严格按照**数字升序**排列，以保证剪辑的时间线正确。

请开始你的精剪工作。
""")

# --- D2S: 粗剪精炼提示词 (Stage 21) ---
ROUGH_CUT_REFINEMENT_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位顶级的电影剪辑师。你的任务是为一个已经写好旁白的场景，从一个包含所有可用素材的“粗剪”序列中，挑选出一个最佳的镜头组合（即“精剪”），以达到最强的音画同步和情感冲击力。

# Input: Scene Narration Script
这是本场景需要匹配的旁白/对话内容：
---
{scene_script_text}
---
旁白预估时长: {narration_duration:.2f} 秒

# Input: Rough Cut (Available Candidate Shots)
以下是本场景所有可用的候选镜头，已按时间顺序排列：
---
{candidate_shots_json}
---
候选镜头总时长: {total_candidate_duration:.2f} 秒

# Task & Rules
1.  **核心任务**: 从“粗剪”列表中，挑选出一个子序列。这个子序列必须在视觉上与“旁白脚本”高度匹配。
2.  **时长匹配**: 你挑选出的镜头序列的总时长，应该**约等于或略长于**旁白的预估时长。这是一个硬性约束。
3.  **叙事连贯**: 确保你选择的镜头序列在视觉上是连贯流畅的。
4.  **保留精华**: 优先选择那些最具信息量、情感最饱满、或动作最关键的镜头。
5.  **顺序要求**: 你返回的 `shot_order_id` 列表必须严格按照**数字升序**排列，以保证剪辑的时间线正确。
6.  **候选镜头过滤**: 提供给你的“候选镜头列表”已经预先过滤，移除了所有在先前节拍中已使用过的镜头。你必须且只能从这个列表中进行选择。

请开始你的精剪工作。
""")

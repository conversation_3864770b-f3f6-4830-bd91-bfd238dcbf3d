"""
时长控制模块 (Stage 20)
"""
from pydantic import BaseModel, Field

from .script_writing import ScriptBeat

from .base import solver_prompt


# ============= 模型定义 =============
class TrimmedBeatResponse(BaseModel):
    trimmed_beat: ScriptBeat = Field(..., description="经过智能修剪后，返回的完整的、更新后的 ScriptBeat 对象。")


# ============= 提示词定义 =============
BEAT_TRIM_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位精于取舍的定剪导演（Supervising Editor）。你的任务是接收一个单一的、过长的纯视觉叙事单元（ScriptBeat），并根据明确的时长削减目标，对其进行精确的、外科手术式的修剪。

# Input: The Overlong Beat (需要你修剪的过长节拍)
---
{beat_to_trim_json}
---

# Input: Trimming Goal (修剪目标)
---
- 当前单元时长: {current_duration:.2f} 秒
- 目标削减时长: 约 {trim_goal_seconds:.2f} 秒
---

# Task & Rules
1.  **【核心任务】返回一个修剪后的节拍**: 你的目标通过以下两种方式来达到时长要求：
    -   **方式一 (首选)**: 移除 `visual_beats` 中的部分镜头（即缩短 `selected_shot_order_ids` 列表）。
    -   **方式二 (必要时)**: 如果仅移除镜头无法满足要求，或某个 `visual_beat` 在叙事上相对次要，你可以直接**从 `visual_beats` 列表中移除整个 `visual_beat` 对象**。你每次最多可以酌情删除1-2个场景。
    你必须返回一个**完整的、经过你修改后的新 ScriptBeat 对象**。
2.  **【最高优先级】保护叙事核心**: 在删减时，无论是删镜头还是删场景，都必须保留最能体现该单元核心 `description` 的内容。
3.  **删减策略**:
    -   优先移除那些信息量较低、重复性或纯粹建立氛围的镜头。
    -   在移除整个 `visual_beat` 时，优先选择那些对主线情节推动作用较小的、或者可以被其他节拍内容所涵盖的场景。
    -   必须严格遵守“动作完整性”原则：在移除镜头时，不能破坏一个完整动作的因果链条。
4.  **【硬性约束】** 在你返回的 `ScriptBeat` 对象中，其内部的每一个 `VisualBeat` 对象都**必须**包含 `selected_shot_order_ids` 字段。即使你决定移除一个视觉节拍中的所有镜头，也必须返回一个空的 `selected_shot_order_ids: []` 列表，而不是省略该字段。

请开始你的精剪工作。
""")

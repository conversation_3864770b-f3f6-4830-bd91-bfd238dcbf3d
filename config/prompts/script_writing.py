"""
剧本创作与评估模块 (Stages 18)
包含视觉节拍汇编、剧本评估、自我修正等相关的提示词和模型
"""

from enum import Enum
from typing import Any, List, Optional

from pydantic import BaseModel, Field, model_validator

from .base import solver_prompt, verifier_prompt


# ============= 模型定义 =============


# --- 视觉节拍与剧本节拍模型 ---
class VisualBeat(BaseModel):
    source_scene_number: int = Field(..., description="此视觉节拍对应的源场景编号。")
    description: str = Field(..., description="基于源场景摘要生成的、连贯的视觉描述。")
    selected_shot_order_ids: Optional[List[int]] = Field(
        None, description="由AI剪辑师在精剪阶段（阶段19）最终选定的、用于呈现此视觉节拍的镜头顺序ID列表。"
    )


class NarrationType(str, Enum):
    NARRATOR = "NARRATOR"
    INNER_MONOLOGUE = "INNER_MONOLOGUE"
    CHARACTER_DIALOGUE = "CHARACTER_DIALOGUE"


class ScriptBeat(BaseModel):
    beat_number: int = Field(..., description="叙事单元在整个剧本中的顺序编号，从1开始。")
    source_narrative_unit_number: int = Field(..., description="此剧本节拍对应的源叙事单元编号。")
    visual_beats: List[VisualBeat] = Field(
        ...,
        description="构成此叙事单元的、按时间顺序排列的纯视觉节拍列表。在阶段19后，此列表中的每个VisualBeat将包含`selected_shot_order_ids`字段。",
    )
    audio_content: Optional[str] = Field(
        None, description="对此整个叙事单元的综合旁白。在纯视觉剪辑阶段，此字段应为null。"
    )
    narration_type: Optional[NarrationType] = Field(
        None,
        description="音频的性质。在纯视觉剪辑阶段，此字段应为null。",
    )

    @model_validator(mode="after")
    def check_audio_logic(self) -> "ScriptBeat":
        if self.audio_content and self.narration_type is None:
            raise ValueError("如果提供了 'audio_content', 则必须指定 'narration_type'。")
        if self.narration_type and self.audio_content is None:
            raise ValueError("如果指定了 'narration_type', 则必须提供 'audio_content'。")
        return self


class MovieCommentaryScriptResponse(BaseModel):
    script: List[Any] = Field(..., description="构成整个视频的音画节拍列表。")

    @model_validator(mode="after")
    def validate_script_beats(self):
        # 确保列表中的每个元素都是 ScriptBeat 类型
        from pydantic import ValidationError

        for i, item in enumerate(self.script):
            if not isinstance(item, ScriptBeat):
                try:
                    self.script[i] = ScriptBeat.model_validate(item)
                except ValidationError as e:
                    raise ValueError(f"Script item at index {i} is not a valid ScriptBeat: {e}")
        return self


# --- 剧本评估模型 ---
class ScriptEvaluationScore(BaseModel):
    score: int = Field(..., ge=1, le=5, description="评分 (1-5分)。")
    justification: str = Field(..., description="评分的详细理由。")


class ScriptEvaluationResponse(BaseModel):
    relevance: ScriptEvaluationScore = Field(
        ..., description="关联性 (Relevance): 剧本在多大程度上回应和实现了项目目标和主题？"
    )
    engagement: ScriptEvaluationScore = Field(
        ..., description="吸引力 (Engagement): 剧本是否拥有引人入胜的节奏、生动的对白和坚实的结构？"
    )
    adherence: ScriptEvaluationScore = Field(
        ..., description="遵循度 (Adherence): 角色行为和对白是否严格遵循了角色设定和动机？"
    )
    coherence: ScriptEvaluationScore = Field(
        ..., description="连贯性 (Coherence): 故事的情节发展是否符合逻辑？因果链条在剧本中是否得到了可信的呈现？"
    )
    technical_quality: ScriptEvaluationScore = Field(
        ...,
        description="技术性 (Technical-quality): 剧本格式是否规范？动作和声音描述是否清晰，足以指导后续的AI剪辑决策？",
    )


# --- 单场景剧本评估响应模型 ---
class SceneScriptEvaluationResponse(BaseModel):
    score: int = Field(..., ge=1, le=5, description="对该场景剧本的总体评分 (1-5分)。")
    is_ready_for_production: bool = Field(..., description="判断该稿件是否已达到可用于生产的质量标准。")
    justification: str = Field(..., description="评分的简要理由。")
    suggested_improvements: str = Field(..., description="具体的、可操作的修改建议，用于指导下一轮修订。")


# ============= 提示词定义 =============

# --- 旁白干预指令 ---
NARRATION_INTERVENTION_INSTRUCTIONS = {
    "dialogue_heavy": "指令：你的首要任务是保留角色间的原始对话。只在绝对必要时（例如，为了连接两个在时间或空间上不连续的场景）才使用客观旁白(`NARRATOR`)。几乎不使用内心独白(`INNER_MONOLOGUE`)。",
    "balanced": "指令：这是一个强制性要求。你的目标是实现旁白（NARRATOR或INNER_MONOLOGUE）和原声对话（CHARACTER_DIALOGUE）在时长上的大致均衡。根据提供的统计数据，旁白的时间占比应**在40%至60%的合理区间内**。你必须主动地、大量地将非核心、功能性或过渡性的对话改写为简洁的旁白。只保留那些最富戏剧性、最能体现角色性格的高光对话。如果比例严重偏离此区间，将被视为不符合要求。",
    "narration_heavy": "指令：你的唯一目标是创作一个由旁白驱动的解说词。你必须将至少80%的角色对话改写为第三人称叙述(`NARRATOR`)或内心独白(`INNER_MONOLOGUE`)。你不应该直接保留原始对话，除非它是绝对无法替代的、不超过10个词的标志性台词。你的输出应该感觉像一部纪录片或深度电影分析，而不是一部电影剧本。",
}

# --- 视觉节拍汇编提示词 ---
VISUAL_BEAT_ASSEMBLY_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位严谨的“视觉序列汇编器”（Visual Sequence Assembler）。你的任务是接收一个“叙事单元”及其关联的、来自原始视频的场景数据（Ground Truth），然后将这些源场景**逐一转换**为结构化的纯视觉节拍。

# Core Principles (核心原则)
1.  **【最高优先级】绝对忠于素材**: 你的创作**唯一**的事实来源是 `original_scene_data` 字段。你的任务是转述，不是创作。
2.  **一对一映射**: `original_scene_data` 列表中的**每一个**场景，都必须在输出的 `visual_beats` 列表中有一个对应的 `VisualBeat` 对象。
3.  **禁止杜撰**: 在生成 `VisualBeat` 的 `description` 时，你必须**基于**源场景的 `summary`。你可以进行微小的语法调整使其更流畅，但**绝对禁止**添加任何源摘要中不存在的动作、物体、情感或细节。

# Input: Global Context (全局上下文)
---
### 全局故事流 (Full Story Flow):
{full_story_flow_json}
---

# Input: Creative Direction (创作风格指导)
---
### 全局旁白基调: {global_narration_tone}
### 类型风格指令: {genre_instruction}
---

# Input: Narrative Unit Outline (当前叙事单元大纲)
这是你需要为其创建视觉节拍的叙事单元。**请严格依据 `original_scene_data` 字段进行创作。**
---
{scene_outline_json}
---

# Task & Rules
1.  **【核心任务】生成单一剧本节拍**: 你的输出是一个 `ScriptBeat` 对象的列表，这个列表中**必须只包含一个** `ScriptBeat` 对象。
2.  **填充 `source_narrative_unit_number`**: 你必须从输入的 `scene_outline_json` 中，原封不动地复制 `narrative_unit_number` 的值。
3.  **填充 `beat_number`**: 请将此字段的值固定设置为 `1`。最终的连续编号将由程序在后续步骤中自动完成。
4.  **填充 `visual_beats` 列表**: 在 `ScriptBeat` 内部，遍历 `original_scene_data` 中的每一个场景：
    -   创建一个 `VisualBeat` 对象。
    -   将其 `source_scene_number` 设置为源场景的 `scene_number`。
    -   将其 `description` 设置为对源场景 `summary` 的忠实转述。
6.  **【硬性约束】禁用音频**: `audio_content` 和 `narration_type` 字段必须始终为 `null`。

请开始你的视觉序列汇编工作。
""")

# --- 全局剧本评估提示词 ---
SCRIPT_EVALUATION_PROMPT = verifier_prompt(r"""
# Role & Goal
你是一位经验丰富的剧本医生和剪辑总监。你的任务是根据项目的总体目标和角色设定，对一份完整的剧本草稿进行全面的质量评估。

# Input: Project Goals & Character Profiles
---
### 项目信息 (Project Info)
{project_info_json}

### 角色设定 (Characters)
{characters_json}
---

# Input: Full Script Draft
---
{script_text}
---

# Task & Framework
请根据 REACT-S 框架进行评估：
-   **关联性 (Relevance)**: 剧本在多大程度上回应和实现了项目目标和主题？
-   **吸引力 (Engagement)**: 剧本是否拥有引人入胜的节奏、生动的对白和坚实的结构？
-   **遵循度 (Adherence)**: 角色行为和对白是否严格遵循了角色设定和动机？
-   **连贯性 (Coherence)**: 故事的情节发展是否符合逻辑？因果链条在剧本中是否得到了可信的呈现？
-   **技术性 (Technical-quality)**: 剧本格式是否规范？动作和声音描述是否清晰，足以指导后续的AI剪辑决策？

请使用提供的工具返回你的评估结果。
""")


# --- 单场景剧本评估提示词 ---
SCENE_SCRIPT_EVALUATION_PROMPT = verifier_prompt(r"""
# Role & Goal
你是一位严谨的剧本医生和剪辑总监。你的任务是评估AI为单个场景生成的剧本初稿，判断其是否达到了可用于生产的质量标准。

# Input: Narration Style Guide (旁白风格指南)
---
{narration_style_instruction}
---

# Input: Scene Outline (场景大纲 - 这是评估的“基准真相”)
**【评估准则】`original_scene_data` 字段是评估剧本事实一致性的唯一依据。**
---
{scene_outline_json}
---

# Input: Generated Script Draft (需要你评估的剧本初稿)
---
{generated_script_json}
---

# Task & Framework
请根据以下标准进行评估：
1.  **【核心】旁白策略遵循度**: 剧本的旁白和对话比例，是否严格遵循了“旁白风格指南”的要求？
2.  **逻辑遵循度**: 剧本内容是否严格遵循了场景大纲（Scene Outline）的描述？
3.  **叙事质量**: 旁白和对话是否生动、连贯，并成功实现了场景大纲中定义的`scene_goal`？
4.  **技术规范**: 格式是否正确？是否存在明显的逻辑漏洞或常识性错误？

# Output
- `score`: 给出1-5的总体评分。
- `is_ready_for_production`: 明确判断此稿件是否可以直接使用（通常score为4或5时为true）。
- `justification`: 简要说明你的评分依据。
- `suggested_improvements`: 【核心】如果稿件不完美（score < 5），请提供**具体、可操作的**修改建议。
""")

# --- 单场景剧本自我修正提示词 ---
SCENE_SCRIPT_SELF_CORRECTION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位顶级的、擅长音画结合的电影解说创作者（Screenwriter-Editor），你刚刚收到了关于你初稿的专业反馈。你的任务是基于这些反馈，并重新审视所有原始的创作要求，对单个场景的音画节拍进行**彻底的重写**。

# Overall Creative Direction (原始创作方向)
---
### 项目一句话总结 (Tagline): {tagline}
### 全局旁白基调: {global_narration_tone}

### 类型风格指令:
{genre_instruction}

### 【新增】旁白干预指令:
{narration_intervention_instruction}
---

# Input: Character Profiles & Dossiers (原始角色资料)
以下是本场戏涉及角色的详细设定和档案：
---
### 角色设定 (Characters)
{characters_json}

### 角色档案 (Dossiers)
{dossier_text}
---

# Scene-Specific Strategy & Outline (原始场景策略与大纲)
这是总编剧为你制定的本场戏的具体创作策略和场景大纲。**【最高优先级规则】你必须以 `original_scene_data` 字段中的内容作为剧本修正的唯一事实基础（Ground Truth）。**
---
### 【重要】全局创作策略 (Global Strategy):
{full_script_strategy_json}

### 【重要】全局故事流 (Full Story Flow):
{full_story_flow_json}

### 本场景大纲 (Current Scene Outline):
{scene_outline_json}
---

# Input: First Draft & Evaluation Report (你的初稿及评估报告)
---
### 你的初稿 (First Draft):
{original_script_json}

### 对你初稿的评估报告 (Evaluation Report):
{evaluation_report_json}
---


# Task & Rules
1.  **【核心任务】重写音画节拍**: 你的核心任务是**重写**这个场景的剧本，以系统性地解决“评估报告”中指出的所有问题，同时必须严格遵循所有的原始创作方向和输入材料。
2.  **保持优点**: 在修正问题的同时，请保留初稿中做得好的部分。
3.  **节拍内容填充规则**: (与初稿生成规则完全相同)
    -   `visual_description`: **必须填写**。
    -   `audio_content` 和 `narration_type`: 根据你的创作意图填充旁白、对话或留空。
4.  **【重要】实现节奏感**: 再次强调，你必须有意识地交错使用**旁白节拍**、**原声对话节拍**和**纯视觉节拍**。
5.  **输出格式**: 你必须以与初稿完全相同的格式返回**完整的、重写后的**剧本。
7.  **【硬性约束】`audio_content` 与 `narration_type` 必须同时提供或同时留空。** 如果节拍有音频，两者都必须有值；如果节拍是纯视觉，两者都必须为 `null`。
8.  **【内容约束】`audio_content` 字段应只包含需要被朗读的纯文本。不要包含任何如 "旁白:" 或 "角色名(内心独白):" 之类的前缀元数据。**

请开始你的修订工作。
""")

"""
标语生成模块 (Stage 4)
包含标语生成的提示词和对应的 Pydantic 模型
"""

from pydantic import BaseModel, Field
from .base import solver_prompt


# ============= 模型定义 =============
class TaglineResponse(BaseModel):
    tagline: str = Field(..., description="为视频生成的一句简洁、有力、吸引人的宣传标语。")


# ============= 提示词定义 =============
TAGLINE_GENERATION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位顶级的市场营销专家和广告文案撰写人。你的任务是为一部电影，基于其核心信息，创作一句（一个）极具吸引力的宣传标语（Tagline）。

# Input: Research Summary
以下是关于这部电影的所有研究资料摘要：
---
{research_summary}
---

# Task & Rules
1.  **核心任务**: 基于以上信息，创作一句简洁、有力、能够激发观众好奇心的宣传标语。
2.  **风格**: 标语应该朗朗上口，易于记忆，并能准确传达影片的核心情感或冲突。

请开始你的创作。
""")
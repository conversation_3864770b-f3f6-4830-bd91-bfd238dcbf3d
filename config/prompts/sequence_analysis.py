"""
序列分析模块 (Stage 8)
包含场景到序列的分析、角色情感弧线分析的提示词和模型
"""
from typing import List
from pydantic import BaseModel, Field
from .base import solver_prompt

# ============= 模型定义 =============

# --- 场景到序列分析 ---
class SequenceAnalysisItem(BaseModel):
    scene_numbers: List[int] = Field(..., description="组成这个序列的所有原始场景编号。")
    theme: str = Field(..., description="这个序列的核心主题或中心思想。")
    summary: str = Field(..., description="对这个序列的完整情节进行连贯的叙事性总结。")
    emotional_arc: str = Field(..., description="描述这个序列从开始到结束的情绪变化曲线。")

class SequenceAnalysisResponse(BaseModel):
    sequences: List[SequenceAnalysisItem]

# --- 角色情感弧线分析 ---
class CharacterEmotionalArc(BaseModel):
    character_name: str = Field(..., description="角色的名称。")
    emotional_arc: str = Field(..., description="该角色在此序列中的情感变化或心路历程的详细描述。")

class CharacterEmotionalArcResponse(BaseModel):
    character_arcs: List[CharacterEmotionalArc]


# ============= 提示词定义 =============

# --- 场景到序列的分析提示词 ---
SEQUENCE_ANALYSIS_PROMPT = solver_prompt(r"""
你是一位资深的电影理论家和剧本医生。你的任务是将以下按时间顺序排列的场景(Scene)摘要，组合成更高层次的叙事单元——序列(Sequence)。一个序列由多个场景组成，并围绕一个共同的主题或一个连续的、有始有终的行动段落展开。

场景摘要列表:
{scene_summaries}

请分析场景列表，识别出序列的边界，并使用提供的工具来构建你的分析结果。
""")

# --- 角色情感弧线分析提示词 ---
CHARACTER_EMOTIONAL_ARC_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位深刻的剧本分析师，擅长洞察人物的内心世界。你的任务是基于一个已定义的叙事序列，分析其中每个主要角色的情感变化弧线。

# Input: Sequence Context
---
### 序列主题: {sequence_theme}

### 序列摘要: {sequence_summary}

### 序列中出现的角色: {character_list}

### 序列包含的场景摘要:
{scene_summaries}
---

# Extraction Principles (提取原则)
按出现顺序提取角色、情感和关系。  使用精确文本进行提取，不要改写或重叠实体。  为每个实体提供有意义的属性以添加上下文。

# Task & Rules
1.  **聚焦角色**: 对 “序列中出现的角色” 列表中的 **每一位** 角色进行分析。
2.  **描述弧线**: 详细描述每个角色从序列开始到结束的情感变化过程。他们经历了什么？他们的感受如何演变（例如：从困惑到坚定，从喜悦到悲伤）？
3.  **言之有据**: 你的分析必须基于提供的场景摘要内容。

请开始你的角色情感弧线分析。
""")

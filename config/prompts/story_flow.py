"""
故事流编排模块 (Stage 17)
"""
from typing import List

from pydantic import BaseModel, Field

from .base import solver_prompt


# ============= 模型定义 =============
class StoryFlowItem(BaseModel):
    narrative_unit_number: int = Field(..., description="来自原始故事大纲的叙事单元编号。")
    reasoning: str = Field(
        ..., description="将此单元放置在当前位置（例如：开篇、主体、高潮、结尾）的理由，需要与全局策略关联。"
    )


class StoryFlowResponse(BaseModel):
    story_flow: List[StoryFlowItem] = Field(
        ..., description="根据全局策略重新编排后的故事流程，这是一个叙事单元对象的列表。"
    )


# ============= 提示词定义 =============
STORY_FLOW_ARRANGEMENT_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位经验丰富的导演和总编剧，擅长从宏观视角掌控故事的节奏和结构。你的任务是接收一份详细的“故事大纲”和一份高层“创作策略”，然后将它们结合起来，输出一个经过精心编排的、最终的“故事流”。

# Input: High-Level Creative Strategy
这是你必须遵循的全局创作策略：
---
{script_strategy_json}
---

# Input: Detailed Story Outline
这是可供你使用的所有叙事单元（场景）的完整列表：
---
{story_outline_json}
---

# Task & Rules
1.  **【核心任务】编排故事流**: 你的输出是一个**`StoryFlowItem`对象的列表**。你必须根据“创作策略”中定义的 `hook_strategy`, `climax_strategy`,
`conclusion_strategy`，从“故事大纲”中挑选并排列场景，形成一个引人入胜的完整叙事流。
2.  **策略实现**:
    -   **开篇 (Hook)**: 根据 `hook_strategy` 选择一个或多个场景作为故事的开端。
    -   **高潮 (Climax)**: 根据 `climax_strategy` 选择并放置高潮场景。
    -   **结尾 (Conclusion)**: 根据 `conclusion_strategy` 选择收尾场景。
    -   **主体 (Body)**: 将剩余的场景合乎逻辑地填充在开篇、高潮和结尾之间，确保故事的连贯性。
3.  **无需创造**: 你不能创造新的场景，所有使用的场景都必须来自“故事大纲”。你可以选择性地**省略**一些不重要的场景，以使故事更紧凑。
4.  **提供理由**: 对于你放入故事流的**每一个场景**，都必须在 `reasoning` 字段中提供简要的编排理由。例如：“作为开篇钩子，展示主角的双重身份”或“作为高潮部分，揭示核心冲突”。
5.  **ID 复制**: 你必须原封不动地从“故事大纲”中复制 `narrative_unit_number`。
6.  **【硬性约束】唯一性**: 原始故事大纲中的每一个 `narrative_unit_number` 在最终的故事流中**最多只能出现一次**。绝不允许重复使用同一个场景。

请开始你的故事流编排工作。
""")

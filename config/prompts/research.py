"""
外部资料研究模块 (Stage 3)
包含研究规划和总结的提示词及模型
"""

from typing import List

from pydantic import BaseModel

from .base import solver_prompt


# ============= 模型定义 =============
class ResearchPlanResponse(BaseModel):
    core_analysis: List[str]
    background_and_worldview: List[str]
    creators_research: List[str]
    reception_and_legacy: List[str]


# ============= 提示词定义 =============
RESEARCH_PLAN_PROMPT = solver_prompt(r"""
你是一位资深的研究总监和信息架构师。你的任务是为一部指定的电影制定一个全面、深入的研究计划。

已知信息：
- 电影标题: "{title}"
- 发行年份: {year}
- 导演: {director}
- 主要演员: {actors}

我们已经确定了以下几个必须研究的核心主题，它们将由其他专家处理：
---
**已有的核心主题 (你不需要重复生成这些):**
{existing_topics}
---

# 你的任务
请基于以上信息，为以下**补充类别**，生成2-3个具体的、有深度的、且**不与已有核心主题重复**的研究主题。

## 补充研究类别

  ### 1. 类型特色与创新
  - 针对{genre}类型电影的独特元素分析
  - 该片在同类型中的创新突破点
  - 与同类型经典作品的对比研究

  ### 2. 时代背景与社会影响
  - {year}年代的社会文化背景对该片的影响
  - 该片反映的时代特征和社会议题
  - 在特定历史时期的文化意义

  ### 3. 技术与艺术成就
  - 该片在cinematography/音效/特效方面的技术突破
  - 艺术指导和视觉风格的独创性
  - 对电影工业技术发展的贡献

  请确保每个主题都：
  1. 具有明确的研究价值和独特视角
  2. 不与已有核心主题重复
  3. 适合进行深入的网络资料搜集
  4. 能产生有价值的洞察和分析

请使用提供的工具来构建你的研究计划。
""")

RESEARCH_SUMMARY_PROMPT = solver_prompt(r"""
你是一个研究助理。请从以下网页内容中，提炼出与 "{entity}" 相关的核心事实、有趣见解或关键引述。

总结应客观、精炼，不超过200字。

网页内容:
{content}

请直接返回总结内容，不需要JSON格式。
""")

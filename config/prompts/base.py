"""
通用提示词基础设施
包含通用指导原则和提示词构建函数
"""

import functools
from config.settings import settings


# ============= 通用指导原则 =============
_SOLVER_PRINCIPLES = fr"""
## 创作准则
1. 剧本一致性：必须严格符合输入的大纲、角色设定及项目要求；任何与既定事实冲突的情节都视为失败。
2. 真实性与克制：当缺乏足够素材或证据支撑某段剧情时，绝不可凭空捏造；宁可保留占位符或说明缺口。
3. 结构化输出：所有产出须遵循调用处指定的 Pydantic 模型 / JSON 模板，禁止添加额外说明或格式。
4. 自检摘要：在输出内部（若模板包含 meta 字段）先给出对完成度的自我评估与方法草图；然后给出正式内容。
5. **语言**: 你的所有输出都必须使用 **{settings.SCRIPT_LANGUAGE}**。
"""

_VERIFIER_PRINCIPLES = r"""
## 审核标准
1. 非建设性审查：你不负责改写或补全，仅指出问题并分类。
2. 错误分类：
  a. 关键错误 – 情节/角色设定与输入资料矛盾，或逻辑链断裂；一旦出现，指出后停止评阅该链条，但继续检查其他独立段落。
  b. 论证/叙事缺陷 – 结论可能正确，但描述过简、跳跃或缺乏依据；指出不足后**假设此结论成立**继续向后检查。
3. 输出格式：必须使用调用方提供的模板（通常为分节说明或 JSON 评估报告），严禁插入额外修正文本。
"""


def _create_prompt(specific_content: str, principles: str) -> str:
    """将具体任务内容与通用原则结合，形成最终提示词。"""
    transition_text = "\n\n---\n在执行以上任务时，请严格遵守以下准则："
    return f"{specific_content.strip()}{transition_text}\n{principles.strip()}"


# 创建便捷的包装函数
solver_prompt = functools.partial(_create_prompt, principles=_SOLVER_PRINCIPLES)
verifier_prompt = functools.partial(_create_prompt, principles=_VERIFIER_PRINCIPLES)

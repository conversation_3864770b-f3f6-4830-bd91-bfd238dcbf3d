"""
数据基础构建模块 (Stage 10)
包含创作说明书生成、角色补全、关系丰富等提示词和模型
"""

from typing import Any, List

from pydantic import BaseModel, Field, model_validator

from .base import solver_prompt


# ============= 模型定义 =============


# --- D2S: 设计文档解析模型 ---
class RelationshipInfo(BaseModel):
    character_id: str = Field(..., description="关联角色的ID。", alias="target_character_id")
    relationship_type: str = Field(..., description="关系类型，例如 '盟友', '敌人', '导师'。", alias="nature")


class CharacterInfo(BaseModel):
    character_id: str = Field(..., description="角色的唯一标识符，例如 'char_anna'。")
    name: str = Field(..., description="角色的名字。")
    description: str = Field(..., description="角色的外貌、性格和背景故事的详细描述。")
    motivations: str = Field(..., description="驱动角色行动的核心欲望或目标。", alias="motivation")
    relationships: List[RelationshipInfo] = Field(..., description="描述该角色与其他角色的关系。")


class ProjectInfo(BaseModel):
    title: str = Field(..., description="项目的正式标题。", alias="project_title")
    logline: str = Field(
        ...,
        description="用一个包含五句话的段落来概括整个故事。第一句介绍背景和主角，接下来三句描述三个核心的冲突或转折点，最后一句总结结局。",
    )
    themes: List[str] = Field(
        ..., description="故事探讨的核心主题，例如 ['忠诚', '牺牲', '成长']。", alias="core_themes"
    )
    target_audience: str = Field(..., description="描述视频的目标观众群体及其特征。")
    narrative_goals: List[str] = Field(..., description="故事旨在传达的关键信息或和实现的情感效果。")
    narration_perspective: str = Field(..., description="叙事视角，例如 'first_person' 或 'third_person'。")


class DesignDocParseResponse(BaseModel):
    project_info: ProjectInfo
    characters: List[CharacterInfo]

    @model_validator(mode="before")
    def restructure_input(cls, data: Any) -> Any:
        if isinstance(data, dict):
            # --- 步骤 1: 重构 project_info ---
            project_overview = data.get("project_overview")
            if "project_info" not in data and project_overview:
                # 初始化一个临时的 project_info 字典
                temp_project_info = {}

                # 从 project_overview 的顶层提取信息
                temp_project_info["target_audience"] = project_overview.get("target_audience")
                temp_project_info["narrative_goals"] = [project_overview.get("core_purpose")]

                # 从内嵌的 original_film_info 提取信息
                original_info = project_overview.get("original_film_info", {})
                temp_project_info["title"] = original_info.get("title")
                temp_project_info["logline"] = original_info.get("core_plot")

                # 从顶层的 core_themes 提取信息
                temp_project_info["themes"] = data.get("core_themes")

                # 将重构好的字典赋给 project_info
                data["project_info"] = {k: v for k, v in temp_project_info.items() if v is not None}

            # --- 步骤 2: 重构 characters ---
            # 同时检查 "character_settings" 和 "character_setup"
            character_settings = data.get("character_settings") or data.get("character_setup")
            if "characters" not in data and character_settings:
                data["characters"] = character_settings

            # --- 步骤 3: 【核心改动】重构并注入 relationships ---
            character_relationships = data.get("character_relationships")
            if character_relationships and data.get("characters"):
                # 创建一个从 character_id 到角色信息字典的映射，以便快速查找
                char_map = {char.get("character_id"): char for char in data["characters"]}

                for rel in character_relationships:
                    source_id = rel.get("source_id")
                    target_id = rel.get("target_id")

                    # 找到源角色
                    if source_id in char_map:
                        source_char = char_map[source_id]
                        # 如果源角色还没有 relationships 列表，则创建一个
                        if "relationships" not in source_char:
                            source_char["relationships"] = []

                        # 构建符合我们模型的关系对象
                        new_relationship = {
                            "target_character_id": target_id,
                            "nature": rel.get("type"),  # 使用别名 'nature' 来匹配 'relationship_type'
                            # 'description' 字段在我们的模型中不存在，所以忽略它
                        }
                        source_char["relationships"].append(new_relationship)
        return data


# --- 新增：D2S 角色补全响应模型 ---
class CharacterCompletionResponse(BaseModel):
    completed_characters: List[CharacterInfo] = Field(..., description="一个包含新补充的、完整的角色信息对象的列表。")


# ============= 提示词定义 =============

# --- 创作说明书(Design Doc)生成提示词 ---
CREATIVE_BRIEF_GENERATION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位顶级的电影制片人和创意总监。你的任务是基于现有的研究资料和用户的宏观创作要求，为一部电影解说短片撰写一份详尽的、结构化的“创作说明书”（即设计文档），并以JSON格式输出。

# Input: Research Data
以下是从多个来源搜集到的关于这部电影的所有研究资料摘要：
---
{research_summary}
---

# Input: User's Creative Brief
以下是用户提出的宏观创作要求：
- **目标受众与传播渠道**: {target_audience}
- **视觉风格与色彩基调**: {visual_style}
- **叙事节奏与剪辑手法**: {narrative_rhythm}
- **成片的目标时长**: {target_duration}
- **目标发布平台**: {platform}
- **叙事视角**: {narration_perspective}

# Task & Rules
1.  **全面提炼**: 将以上所有信息，提炼并整合成一份结构化的创作说明书。这份说明书将作为后续所有AI自动化步骤的"唯一真实之源"。
2.  **【核心】Logline 创作**: 在填充 `logline` 字段时，你必须遵循"雪花写作法"的第二步，撰写一个精确的五句子段落：
    -   第一句：介绍故事的背景、主角和初始状态。
    -   第二句：描述第一个主要的灾难或转折点，打破主角的平衡。
    -   第三句：描述第二个主要的灾折点，使情况更加复杂。
    -   第四句：描述故事的高潮，即第三个也是最终的灾难或转折点。
    -   第五句：描述故事的结局和主角的最终状态。
3.  **ID创造**: 为每个角色创建一个唯一的、易于理解的`character_id`，例如 "char_anna", "char_alex"。
4.  **角色命名**: 除非研究资料中明确提供了官方中文译名，否则所有角色的 `name` 字段必须保持其原文名称（例如：'Lenny Miller' 而不是 '莱尼·米勒'）。
5.  **关系映射**: 在填充角色的`relationships`字段时，确保使用的`character_id`与你为其他角色创建的ID一致。
7.  **【新增】保存视角**: 你必须将用户指定的 `narration_perspective` 值，原封不动地保存到最终输出的 `project_info` 对象中。

请开始你的分析和创作。
""")

# --- D2S: 角色信息补全提示词 ---
CHARACTER_COMPLETION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位严谨的剧本分析师。你的任务是基于已有的创作说明书（Design Doc），为其中提到但尚未定义的角色，补充完整的角色信息。

# Input: Existing Design Doc Context
以下是目前已经生成的创作说明书内容：
---
{existing_design_doc_json}
---

# Task & Rules
1.  **核心任务**: 请为以下 **缺失的角色ID** 列表中的每一个角色，生成详细的角色信息。
    - **缺失的角色ID**: {missing_character_ids}
2.  **保持一致**: 你生成的角色描述和关系，必须与已有的上下文保持逻辑一致。
3.  **角色命名**: 除非研究资料中明确提供了官方中文译名，否则所有新补充角色的 `name` 字段必须保持其原文名称。

请开始你的角色信息补全工作。
""")

# --- D2S 角色关系丰富提示词 ---
RELATIONSHIP_ENRICHMENT_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位专业的剧本分析师，擅长洞察人物的内在动机和动态关系。你的核心任务是深入分析一个详细的叙事大纲，并基于此，对现有的角色设定文件进行审查和扩充，特别是要丰富角色之间的关系描述。

# Input: Existing Character Profiles
这是当前的角色设定文件。请注意，其中一些角色关系可能是缺失的，或者仅仅是基于共现的通用描述（例如，“场景中共同出现”）。
---
{characters_json}
---

# Input: Narrative Outline (Sequences and Scenes)
这是整个故事的叙事大纲，你必须依据它来推断和描述角色间的具体互动和关系演变。
---
{narrative_outline}
---

# Extraction Principles (提取原则)
按出现顺序提取角色、情感和关系。  使用精确文本进行提取，不要改写或重叠实体。  为每个实体提供有意义的属性以添加上下文。

# Task & Rules
1.  **分析动态关系**: 仔细阅读叙事大纲，理解角色在每个场景和序列中是如何互动的，他们的关系是如何发展的。
2.  **丰富关系描述**: 遍历每一个角色，更新其 `relationships` 列表。
    -   如果一个关系已存在但描述宽泛，请根据故事情节将其变得更具体、更有深度。例如，将“与Oleg共同出现”改为“在Oleg的胁迫下被迫合作”。
    -   如果一个关系在故事中很明显，但在设定中缺失，请补充进去。
3.  **具体化**: 关系描述应反映故事中的具体事件，使用动态的、描述性的语言。例如，优选“在最终对决中背叛了Anna”，而不是“敌人”。
4.  **保持原文名称**: 绝对不能翻译或修改任何角色的 `name` 字段。
5.  **保持结构**: 你的输出必须是完整的、更新后的角色对象列表，严格遵循原始JSON结构。**绝对不能修改 `character_id`**。
6.  **返回完整对象**: 你必须返回包含所有角色（无论是否修改）的完整列表，而不仅仅是发生变动的角色。

请开始你的角色关系分析与丰富工作。
""")

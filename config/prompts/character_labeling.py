"""
AI 角色自动标注模块
"""

from pydantic import BaseModel, Field
from .base import solver_prompt


# ============= 模型定义 =============
class AILabelingResponse(BaseModel):
    is_known_character: bool = Field(..., description="判断图片中的角色是否是已知角色列表中的一员。")
    character_name: str = Field(
        ...,
        description="如果 'is_known_character' 为 true，则返回已知角色的确切名称；否则，为此未知角色提供一个简洁的、基于外貌或行为的描述性名称（例如：'穿红夹克的男人'）。",
    )
    justification: str = Field(..., description="做出此判断的理由，需要结合图像内容和叙事上下文。")


# ============= 提示词定义 =============
CHARACTER_AI_LABELING_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位经验丰富的选角导演和剧本监督，拥有识别人脸和理解剧情的敏锐洞察力。你的任务是识别一张图片中的未知角色。

# Input: Context (上下文信息)
---
### 已知角色列表:
{known_characters_list}

### 叙事上下文:
- **所属序列主题**: {sequence_theme}
- **所属场景摘要**: {scene_summary}
- **镜头描述**: {shot_description}
- **镜头中的对话**: {shot_dialogue}
---

# Input: Image
你将通过多模态接口接收一张图片，图片中用绿框标出了需要你识别的角色。

# Task & Rules
1.  **核心任务**: 仔细观察图片中的角色，并结合提供的叙事上下文，判断其身份。
2.  **决策流程**:
    a.  首先，判断该角色是否属于“已知角色列表”中的一员。
    b.  如果**是**，请在 `character_name` 字段中准确返回该角色的名字。
    c.  如果**不是**已知角色，或者你无法100%确定，请在 `character_name` 字段中为该角色创建一个简洁的、描述性的称谓（例如：“戴帽子的老人”、“服务员”）。**不要**返回 "未知角色"。
3.  **提供依据**: 在 `justification` 字段中，详细说明你做出判断的理由。例如：“根据服装和场景描述，此人应为在餐厅工作的服务员”或“此人的面部特征与已知角色'安娜'高度匹配，且场景摘要中提到了她”。
4.  **严格匹配**: 当匹配已知角色时，返回的 `character_name` 必须与列表中的名字完全一致。
""")

"""
场景分析模块 (Stage 1)
包含镜头分析的提示词和对应的 Pydantic 模型
"""

from pydantic import BaseModel, Field
from .base import solver_prompt


# ============= 通用指令 =============
GENRE_INSTRUCTIONS = {
    "personal": "分析重点：这是一个个人生活记录或Vlog。请关注人物的真实情感流露、环境与人物心境的互动，以及日常活动中的叙事线索。",
    "drama": "分析重点：这是一个剧情片。请深度解读角色的潜台词、行为背后的动机、以及推动情节发展的核心戏剧冲突。",
    "action": "分析重点：这是一个动作片。请详细描述动作场景的激烈程度、编排创意、以及其对角色状态和故事节奏的影响。",
    "comedy": "分析重点：这是一个喜剧片。请识别并分析笑点的类型（例如：语言幽默、情景喜剧、角色反差），以及它们是如何构建和释放的。",
    "documentary": "分析重点：这是一个纪录片。请关注信息的客观呈现、画面的证据价值，以及解说与画面的配合。",
    "thriller": "分析重点：这是一个惊悚片。请关注悬念的设置、紧张氛围的营造，以及引导观众情绪的关键视听元素。",
}


# ============= 模型定义 =============
class ShotAnalysisModel(BaseModel):
    shot_type: str = Field(..., description="景别 (从 特写, 近景, 中景, 全景, 远景 中选择)。")
    camera_angle: str = Field(..., description="机位角度 (从 平视, 俯视, 仰视 中选择)。")
    camera_movement: str = Field(..., description="运镜方式 (从 固定镜头, 推, 拉, 摇, 移, 跟, 升降 中选择)。")
    composition: str = Field(..., description="构图特点 (例如：三分法, 对称构图等)。")
    lighting: str = Field(..., description="光线风格 (例如：高调, 低调, 自然光等)。")


class SceneAnalysisResponse(BaseModel):
    visual_description: str = Field(
        ..., description="对整个镜头画面的综合性、叙事性、文学性的描述，总结核心视觉元素、氛围和潜在的象征意义。"
    )
    narrative_function: str = Field(
        ..., description="解读该镜头在叙事结构中的核心功能。例如：'建立镜头'、'角色引入'、'关键转折'、' foreshadowing(预示)'、'情绪锚点'等。"
    )
    aesthetic_analysis: str = Field(
        ..., description="对镜头美学风格的分析，包括色彩运用、光影情绪、构图的艺术表达等，解读其如何服务于影片的整体美学。"
    )
    people: str = Field(..., description="识别画面中的主要人物，并深入描述他们的状态、微表情或肢体语言所揭示的内在信息。")
    setting: str = Field(..., description="详细描述场景环境、地点和时间，并分析环境如何反映或影响角色的心境。")
    main_action: str = Field(..., description="描述该镜头内的核心动作或事件，并点出其对情节的推动作用。")
    emotion: str = Field(
        ...,
        description="分析并提炼该镜头所传达的核心情绪或情感基调，不仅识别表面情绪，更要推断其背后的复杂情感。",
    )
    key_objects: str = Field(..., description="列出与核心动作或具有叙事/象征意义的关键物体。")
    on_screen_text: str = Field(
        ..., description="识别并转录画面中出现的任何重要文字。如果没有则返回空字符串。"
    )
    shot_analysis: ShotAnalysisModel = Field(..., description="镜头的技术性分析。")
    genre_specific_analysis: str = Field(
        ..., description="基于指定的影片类型进行的特殊分析。例如，动作片分析其动作强度，剧情片分析其潜台词等。"
    )


# ============= 提示词定义 =============
SCENE_ANALYSIS_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位电影理论与实践的解构大师、资深的电影学院教授。你的任务是针对所提供的视频片段，进行一次教科书级别的、深入的、结构化的纯视觉分镜头分析。请将你的洞察力聚焦于视听语言如何塑造叙事、传递情感和营造氛围。

# Input
---
### 视频时长: {duration}秒

### 【风格倾向指导】
{genre_instruction}
---

# Task & Deconstruction Framework (任务与解构框架)
你的核心任务是**逐项解构**画面的构成元素，并解读其内在的叙事功能与美学价值。你的分析应当精准、专业，并严格按照提供的工具/格式进行输出。

**提取原则**: 按出现顺序提取角色、情感和关系。  使用精确文本进行提取，不要改写或重叠实体。  为每个实体提供有意义的属性以添加上下文。

具体要求如下：

-   **`visual_description`**: 提供一段综合性的、叙事性的描述，捕捉画面的整体感觉。
-   **`narrative_function`**: 精准判断这个镜头在故事中的作用是什么？（例如：'建立镜头', '角色引入', '关键转折', '预示'）。
-   **`aesthetic_analysis`**: 分析镜头的美学风格，如色彩、光影如何服务于情感表达。
-   **`people`**: 深入描述人物的状态和微表情。
-   **`setting`**: 描述环境，并分析其与人物心境的关系。
-   **`main_action`**: 描述核心动作，并点出其情节推动作用。
-   **`emotion`**: 提炼镜头传达的核心情绪基调。
-   **`key_objects`**: 列出有象征意义的关键物体。
-   **`on_screen_text`**: 识别所有屏幕上的文字。
-   **`shot_analysis`**: 填充所有技术性镜头语言参数。
-   **`genre_specific_analysis`**: 根据提供的"风格倾向指导"，填写此字段。例如，如果是动作片，则分析其"动作激烈程度"；如果是剧情片，则分析其"潜台词"。

请开始你的专业解构分析。
""")

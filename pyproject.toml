[project]
name = "uni-api"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "aiofiles>=24.1.0",
    "aiosqlite>=0.21.0",
    "asyncpg>=0.30.0",
    "cryptography==43.0.3",
    "fastapi>=0.115.12",
    "greenlet>=3.2.2",
    "httpx-socks==0.9.2",
    "httpx[http2]>=0.27.2",
    "pillow>=11.2.1",
    "pytest>=8.3.5",
    "python-dotenv>=1.1.1",
    "python-multipart>=0.0.20",
    "ruamel-yaml>=0.18.10",
    "sqlalchemy>=2.0.40",
    "uvicorn>=0.34.2",
    "watchfiles>=1.0.5",
]


[[tool.uv.index]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple"

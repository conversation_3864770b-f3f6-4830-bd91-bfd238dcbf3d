#!/usr/bin/env python3
"""
文件重命名工具测试脚本

创建测试文件并验证重命名功能
"""

import tempfile
import shutil
from pathlib import Path
from utils.file_renamer import FileRenamer


def create_test_files(test_dir: Path) -> None:
    """创建测试文件"""
    test_files = [
        "20250223 宏观策略金工(1).docx",
        "20250223 宏观策略金工(2).docx", 
        "报告文档(3).pdf",
        "会议纪要(1).txt",
        "会议纪要.txt",  # 这个文件存在，所以 "会议纪要(1).txt" 不应该被重命名
        "正常文件.docx",  # 没有括号，不应该被处理
        "文件(abc).docx",  # 括号内不是数字，不应该被处理
    ]
    
    for filename in test_files:
        file_path = test_dir / filename
        file_path.write_text(f"测试内容 - {filename}")
        print(f"创建测试文件: {filename}")


def test_file_renamer():
    """测试文件重命名功能"""
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir)
        print(f"测试目录: {test_dir}")
        
        # 创建测试文件
        print("\n=== 创建测试文件 ===")
        create_test_files(test_dir)
        
        # 显示初始文件列表
        print(f"\n=== 初始文件列表 ===")
        for file_path in sorted(test_dir.iterdir()):
            if file_path.is_file():
                print(f"  {file_path.name}")
        
        # 测试预览模式
        print(f"\n=== 预览模式测试 ===")
        renamer = FileRenamer(test_dir, dry_run=True)
        results = renamer.process()
        print(f"预览结果: {results}")
        
        # 测试实际重命名
        print(f"\n=== 实际重命名测试 ===")
        renamer = FileRenamer(test_dir, dry_run=False)
        results = renamer.process()
        print(f"重命名结果: {results}")
        
        # 显示最终文件列表
        print(f"\n=== 最终文件列表 ===")
        for file_path in sorted(test_dir.iterdir()):
            if file_path.is_file():
                print(f"  {file_path.name}")
        
        # 验证结果
        print(f"\n=== 结果验证 ===")
        expected_renames = [
            "20250223 宏观策略金工.docx",  # 从 (1) 重命名而来
            "20250223 宏观策略金工(2).docx",  # 保持不变，因为上面的文件存在
            "报告文档.pdf",  # 从 (3) 重命名而来
        ]
        
        should_not_change = [
            "会议纪要(1).txt",  # 不应该重命名，因为 "会议纪要.txt" 已存在
            "会议纪要.txt",  # 原本就存在
            "正常文件.docx",  # 没有括号
        ]
        
        for expected in expected_renames:
            if (test_dir / expected).exists():
                print(f"✅ 正确重命名: {expected}")
            else:
                print(f"❌ 重命名失败: {expected}")
        
        for should_exist in should_not_change:
            if (test_dir / should_exist).exists():
                print(f"✅ 正确保持: {should_exist}")
            else:
                print(f"❌ 意外更改: {should_exist}")


def test_with_extensions():
    """测试文件扩展名过滤功能"""
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir)
        print(f"\n=== 扩展名过滤测试 ===")
        print(f"测试目录: {test_dir}")
        
        # 创建不同类型的测试文件
        test_files = [
            "文档(1).docx",
            "图片(1).jpg", 
            "表格(1).xlsx",
            "文本(1).txt",
        ]
        
        for filename in test_files:
            (test_dir / filename).write_text("测试内容")
            print(f"创建: {filename}")
        
        # 只处理 .docx 文件
        print(f"\n只处理 .docx 文件:")
        renamer = FileRenamer(test_dir, extensions=['.docx'], dry_run=True)
        results = renamer.process()
        
        print(f"\n处理结果: {results}")


if __name__ == '__main__':
    print("开始测试文件重命名工具...")
    test_file_renamer()
    test_with_extensions()
    print("\n测试完成！")

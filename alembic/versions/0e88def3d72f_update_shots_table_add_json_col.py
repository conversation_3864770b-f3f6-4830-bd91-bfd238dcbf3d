"""update shots table add json col

Revision ID: 0e88def3d72f
Revises: bab2e16a1577
Create Date: 2025-08-03 17:42:40.570661

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0e88def3d72f'
down_revision = 'bab2e16a1577'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('shots', sa.Column('analysis_data', sa.JSON(), nullable=True))
    op.add_column('shots', sa.Column('refined_analysis_data', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('shots', 'refined_analysis_data')
    op.drop_column('shots', 'analysis_data')
    # ### end Alembic commands ###
"""
阶段18：D2S 重写者模块 (Rewriter)
第二步：故事流编排
"""

import json
from typing import Any, Dict, Optional, List

from config.prompts import STORY_FLOW_ARRANGEMENT_PROMPT, StoryFlowResponse
from config.schemas import ModelName
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import secondary_client


class Stage18StoryFlow(BaseStage):
    """阶段18：故事流编排"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        self.flow_client = secondary_client
        self.flow_client.model = ModelName.DOUBAO_SEED_1_6.value  # 已修改

    @property
    def stage_number(self) -> int:
        return 18

    @property
    def stage_name(self) -> str:
        return "D2S Rewriter (故事流编排)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage17_status = self.db_manager.get_stage_status(self.video_id, 17)
        if not stage17_status or stage17_status["status"] != "completed":
            return False, "阶段17 (D2S Rewriter 策略规划) 尚未完成"

        story_outline_data = self.db_manager.get_stage_output(self.video_id, 16, "story_outline")
        if not story_outline_data:
            return False, "数据库中未找到故事大纲数据"

        script_strategy = self.db_manager.get_stage_output(self.video_id, 17, "script_strategy")
        if not script_strategy:
            return False, "数据库中未找到剧本创作策略数据"

        return True, ""

    def _validate_unique_narrative_unit_numbers(self, story_flow: List[dict]) -> bool:
        """验证story_flow中narrative_unit_number的唯一性"""
        if not story_flow:
            return True

        unit_numbers = [
            item.get("narrative_unit_number") for item in story_flow if item.get("narrative_unit_number") is not None
        ]
        unique_numbers = set(unit_numbers)

        if len(unit_numbers) != len(unique_numbers):
            duplicates = [item for item in unique_numbers if unit_numbers.count(item) > 1]
            self.logger.error(f"故事流中发现重复的narrative_unit_number: {duplicates}")
            return False

        return True

    def _validate_story_flow_integrity(self, story_flow: List[dict], story_outline: Dict[str, Any]) -> bool:
        """
        验证故事流中的所有 narrative_unit_number 是否都存在于原始的故事大纲中。
        """
        if not story_outline or "story_outline" not in story_outline:
            self.logger.error("无法进行完整性验证：原始故事大纲为空或格式不正确。")
            return False

        # 从原始大纲中提取所有有效的叙事单元编号
        valid_unit_numbers = {item.get("narrative_unit_number") for item in story_outline["story_outline"]}

        # 检查故事流中的每个单元编号
        for item in story_flow:
            unit_num = item.get("narrative_unit_number")
            if unit_num not in valid_unit_numbers:
                self.logger.error(f"完整性验证失败：AI生成的故事流中包含一个不存在的叙事单元编号: {unit_num}。")
                return False

        self.logger.info("故事流完整性验证通过。")
        return True

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行故事流的编排，并包含健壮的验证和去重逻辑。"""
        if force_level:
            self.logger.info(f"强制模式 ({force_level}): 清理旧的故事流数据。")
            self.db_manager.clear_stage_output(self.video_id, self.stage_number, "story_flow")

        existing_output = self.db_manager.get_stage_output(self.video_id, self.stage_number, "story_flow")
        if existing_output:
            self.logger.info("从数据库加载已缓存的故事流数据。")
            return True

        # 1. 加载所需数据
        try:
            story_outline_data = self.db_manager.get_stage_output(self.video_id, 16, "story_outline")
            script_strategy = self.db_manager.get_stage_output(self.video_id, 17, "script_strategy")
            if not story_outline_data or not script_strategy:
                self.logger.error("无法从数据库加载前置阶段的输出数据。")
                return False
        except Exception as e:
            self.logger.error(f"加载前置数据失败: {e}")
            return False

        # 2. 调用AI进行故事流编排
        self.update_progress("AI正在根据全局策略编排故事流...")
        try:
            prompt = STORY_FLOW_ARRANGEMENT_PROMPT.format(
                script_strategy_json=json.dumps(script_strategy, ensure_ascii=False, indent=2),
                story_outline_json=json.dumps(story_outline_data, ensure_ascii=False, indent=2),
                language=settings.SCRIPT_LANGUAGE,
            )

            result = self.flow_client.call_ai_with_tool(prompt, response_model=StoryFlowResponse)

            if not result or not result.story_flow:
                self.logger.error("AI未能生成任何有效的故事流。")
                return False

            # --- 【核心修改】增加自动去重逻辑 ---
            unique_story_flow = []
            seen_numbers = set()
            for item in result.story_flow:
                if item.narrative_unit_number not in seen_numbers:
                    unique_story_flow.append(item)
                    seen_numbers.add(item.narrative_unit_number)

            if len(unique_story_flow) < len(result.story_flow):
                self.logger.warning(
                    f"AI返回的故事流中包含重复的叙事单元。已自动去重，"
                    f"单元数量从 {len(result.story_flow)} 减少到 {len(unique_story_flow)}。"
                )
                result.story_flow = unique_story_flow
            # --- 修改结束 ---

            # 3. 验证AI生成结果的完整性
            story_flow_dicts = [item.model_dump() for item in result.story_flow]
            if not self._validate_story_flow_integrity(story_flow_dicts, story_outline_data):
                self.logger.error("AI生成的故事流完整性验证失败（可能包含不存在的单元编号）。")
                return False

            # 4. 保存到数据库
            self.db_manager.save_stage_output(
                video_id=self.video_id,
                stage_number=self.stage_number,
                stage_name=self.stage_name,
                output_type="story_flow",
                output_data=result.model_dump(),
            )

            self.logger.info(f"✅ 成功编排了包含 {len(result.story_flow)} 个场景的故事流并保存到数据库。")
            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

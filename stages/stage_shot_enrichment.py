"""
阶段10：镜头内容增强 (Shot Enrichment)
利用场景、序列和角色信息，二次增强对每个镜头的理解。
"""

import concurrent.futures
import json
from typing import Any, Dict, Optional

from config.prompts import SHOT_ENRICHMENT_PROMPT, ShotEnrichmentResponse
from config.schemas import ModelName
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import volcengine_client
from utils.shared_state import shutdown_event


class Stage10ShotEnrichment(BaseStage):
    """阶段10：镜头内容增强"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        # 此阶段任务较轻，可使用默认AI客户端
        self.enrichment_client = volcengine_client
        self.enrichment_client.model = ModelName.DOUBAO_SEED_1_6.value  # 不可删除
        # self.enrichment_client.model = ModelName.DOUBAO_SEED_1_6_THINKING.value  # 不可删除
        # self.enrichment_client.model = ModelName.DOUBAO_SEED_1_6_FLASH.value  # 不可删除

    @property
    def stage_number(self) -> int:
        return 10

    @property
    def stage_name(self) -> str:
        return "镜头内容增强"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件：序列分析和AI角色标注必须完成"""
        stage8_status = self.db_manager.get_stage_status(self.video_id, 8)
        if not stage8_status or stage8_status["status"] != "completed":
            return False, "阶段8 (序列分析) 尚未完成"
        stage9_status = self.db_manager.get_stage_status(self.video_id, 9)
        if not stage9_status or stage9_status["status"] != "completed":
            return False, "阶段9 (AI 角色标注) 尚未完成"
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行镜头内容增强流程"""
        shots_to_process = self.db_manager.get_shots_for_enrichment(self.video_id)
        if not shots_to_process:
            self.logger.info("没有需要增强的镜头。")
            return True

        self.update_progress(f"开始增强 {len(shots_to_process)} 个镜头的描述...")

        enriched_results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=settings.MAX_CONCURRENT_REQUESTS) as executor:
            future_to_shot = {executor.submit(self._enrich_single_shot, shot): shot for shot in shots_to_process}

            for i, future in enumerate(concurrent.futures.as_completed(future_to_shot)):
                if shutdown_event.is_set():
                    self.logger.warning("检测到关闭信号，中断镜头增强。")
                    return False
                try:
                    result = future.result()
                    if result:
                        enriched_results.append(result)
                    self.update_progress(f"处理中 - 已完成 {i + 1}/{len(shots_to_process)}")
                except Exception as exc:
                    shot_info = future_to_shot[future]
                    self.logger.error(f"增强镜头ID {shot_info['id']} 时出错: {exc}", exc_info=True)

        if enriched_results:
            self.logger.info(f"AI增强完成，准备将 {len(enriched_results)} 条更新写入数据库...")
            # 【核心修改】调用新的方法来更新 refined_analysis_data 字段
            self.db_manager.update_shot_refined_analysis(enriched_results)
            self.logger.info("数据库更新完成。")
        else:
            self.logger.warning("没有成功增强任何镜头描述。")

        return True

    def _enrich_single_shot(self, shot_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """调用AI增强单个镜头的完整分析，支持多模态输入。"""
        # 1. 【核心修正】准备原始分析报告的JSON字符串
        # 优先使用已有的修正数据作为基础，否则使用原始分析数据
        base_analysis_data = shot_data.get("refined_analysis_json") or shot_data.get("original_analysis_json")
        if not base_analysis_data:
            self.logger.warning(f"镜头ID {shot_data['id']} 缺少原始分析数据，跳过增强。")
            return None

        original_analysis_json = json.dumps(base_analysis_data, ensure_ascii=False, indent=2)

        # 2. 格式化文本提示词
        prompt_text = SHOT_ENRICHMENT_PROMPT.format(
            sequence_theme=shot_data.get("sequence_theme", "无"),
            sequence_summary=shot_data.get("sequence_summary", "无"),
            scene_goal=shot_data.get("scene_goal", "无"),
            scene_summary=shot_data.get("scene_summary", "无"),
            characters_present=", ".join(shot_data.get("character_names", [])) or "无",
            original_analysis_json=original_analysis_json,
            language=settings.SCRIPT_LANGUAGE,
        )

        try:
            clip_url = shot_data.get("low_quality_clip_url")
            response = None

            # 3. 根据是否有视频URL，决定调用方式
            if not clip_url:
                self.logger.warning(f"镜头ID {shot_data['id']} 缺少低质量视频URL，将仅使用文本进行增强。")
                response = self.enrichment_client.call_ai_with_tool(prompt_text, response_model=ShotEnrichmentResponse)
            else:
                multi_part_content = [
                    {
                        "type": "video_url",
                        "video_url": {
                            "url": clip_url,
                            "fps": settings.SCENE_VIDEO_FPS,
                            "detail": settings.SCENE_VIDEO_DETAIL,
                        },
                    },
                    {"type": "text", "text": prompt_text},
                ]
                response = self.enrichment_client.call_ai_with_tool(
                    multi_part_content, response_model=ShotEnrichmentResponse
                )

            # 4. 【核心修改】处理响应并构造用于数据库更新的字典
            if response:
                self.logger.debug(f"镜头ID {shot_data['id']} 增强成功。理由: {response.justification}")

                # 直接返回包含ID和完整AI响应字典的结构
                # model_dump() 会将Pydantic模型转换为一个字典，适合存入JSON字段
                return {
                    "id": shot_data["id"],
                    "refined_analysis_data": response.model_dump(),
                }
        except Exception as e:
            self.logger.error(f"调用AI增强镜头ID {shot_data['id']} 失败: {e}")

        return None

"""
阶段1：镜头分析 (Shot Analysis)
包含镜头检测、分割、AI分析和向量化
"""

import concurrent.futures
import csv
import math
import queue
import subprocess
import threading
from pathlib import Path

from config.prompts.scene_analysis import GENRE_INSTRUCTIONS, SCENE_ANALYSIS_PROMPT
from typing import Any, Dict, List, Optional, Tuple

import numpy as np

from config.schemas import ModelName
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import volcengine_client
from utils.shared_state import shutdown_event
from utils.storage_utils import get_tos_client
from utils.video_utils import video_processor


class Stage1Analysis(BaseStage):
    """阶段1：镜头分析 (Shot Analysis)"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        self._db_queue = queue.Queue()
        self._stop_db_writer = threading.Event()
        # 为视觉分析和向量化初始化一个专用的AI客户端
        self.vision_client = volcengine_client
        # self.vision_client.model = ModelName.DOUBAO_SEED_1_6.value  # 不可删除
        # self.vision_client.model = ModelName.DOUBAO_SEED_1_6_THINKING.value  # 不可删除
        self.vision_client.model = ModelName.DOUBAO_SEED_1_6_FLASH.value  # 不可删除

    def _db_writer_worker(self):
        """从队列中获取并执行数据库写入任务"""
        self.logger.info("数据库写入线程已启动。")
        while not self._stop_db_writer.is_set() or not self._db_queue.empty():
            try:
                db_task, args = self._db_queue.get(timeout=1)
                try:
                    db_task(*args)
                except Exception as e:
                    self.logger.error(f"执行数据库任务时出错: {e}")
                finally:
                    self._db_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"数据库写入线程的队列操作出错: {e}")
        self.logger.info("数据库写入线程已停止。")

    def _calculate_adaptive_batch_size(self, total_items: int, target_batch_size: int) -> int:
        """
        计算自适应批量大小，避免最后一个批次过小。
        从目标批量大小开始递减，直到最后一个批次的数量不小于当前批次的2/3，
        或批量大小降至20为止。
        """
        # 1. 处理边界情况
        if total_items == 0 or target_batch_size <= 0:
            return 1  # 返回安全的默认值
        if total_items <= target_batch_size:
            self.logger.info(f"总数 ({total_items}) 小于或等于目标批量 ({target_batch_size})，将一次性处理。")
            return total_items

        # 2. 迭代寻找最佳批量大小
        # 从 target_batch_size 递减到 20
        for batch_size in range(target_batch_size, 19, -1):
            remainder = total_items % batch_size

            # 如果能整除，这是最理想的情况
            if remainder == 0:
                self.logger.info(f"自适应批量大小调整: 找到可整除的批量大小 {batch_size}。")
                return batch_size

            # 如果最后一个批次的大小符合要求 (不小于当前批次的2/3)
            if remainder >= (batch_size * 2 / 3):
                self.logger.info(
                    f"自适应批量大小调整: "
                    f"批量大小 {batch_size} 产生的最后一个批次大小为 {remainder}，符合要求 (>= 2/3)。"
                )
                return batch_size

        # 3. 如果循环结束仍未找到满足条件的批量大小，则返回循环的最小值 (20)
        final_batch_size = 20
        self.logger.info(f"自适应批量大小调整: 未能在递减过程中找到更优解，将使用最小批量大小 {final_batch_size}。")
        return final_batch_size

    def _get_text_for_embedding(self, data: Dict[str, Any]) -> str:
        """从分析结果或镜头数据中，构建用于生成文本嵌入的标准化描述性文本。"""
        # 仅选择最具识别度的核心字段来构建文本
        descriptive_parts = [
            data.get("visual_description"),
            data.get("people"),
            data.get("setting"),
            data.get("main_action"),
            data.get("key_objects"),
        ]

        return " ".join(filter(None, descriptive_parts)).strip()

    def _split_and_update_long_shot(self, shot_record: Dict[str, Any], fps: float) -> List[Dict[str, Any]]:
        """
        接收一个已存在的长镜头记录，将其物理分割成多个子镜头，
        并在数据库中创建新的子镜头记录，最后删除原始长镜头记录。
        返回新创建的子镜头信息列表。
        """
        MAX_SHOT_DURATION = 10.0
        original_start_time = shot_record["start_time"]
        original_end_time = shot_record["end_time"]
        duration = original_end_time - original_start_time
        original_clip_path = Path(shot_record["clip_url"])
        clips_dir = original_clip_path.parent

        if not original_clip_path.exists():
            self.logger.error(f"原始长镜头片段文件不存在: {original_clip_path}，无法分割。")
            return []

        # 计算分割参数
        num_sub_shots = math.ceil(duration / MAX_SHOT_DURATION)
        sub_shot_duration = duration / num_sub_shots
        self.logger.info(
            f"物理分割：镜头 {original_clip_path.name} (时长 {duration:.2f}s) 将被分割成 {num_sub_shots} 个子镜头。"
        )

        new_sub_shots_info = []
        for i in range(num_sub_shots):
            # 计算在整个视频时间线上的绝对起止时间 (用于数据库)
            sub_start_time = round(original_start_time + i * sub_shot_duration, 3)
            sub_end_time = round(original_start_time + (i + 1) * sub_shot_duration, 3)

            # 【核心修复】计算在长镜头片段文件内部的相对起止时间 (用于ffmpeg)
            clip_relative_start = round(i * sub_shot_duration, 3)
            clip_relative_end = round((i + 1) * sub_shot_duration, 3)

            if i == num_sub_shots - 1:
                sub_end_time = original_end_time  # 绝对时间修正
                clip_relative_end = duration  # 相对时间修正，确保切到文件末尾

            # 使用ffmpeg从原始长片段中提取子片段
            sub_clip_path = clips_dir / f"{original_clip_path.stem}_sub_{i + 1}.mp4"
            success = video_processor.extract_clip(
                original_clip_path, clip_relative_start, clip_relative_end, sub_clip_path
            )

            if success:
                # 在数据库中创建新的子镜头记录
                sub_shot_id = self.db_manager.create_sub_shot_record(
                    video_id=self.video_id,
                    start_time=sub_start_time,
                    end_time=sub_end_time,
                    clip_path=str(sub_clip_path.resolve()),
                )
                if sub_shot_id:
                    new_sub_shots_info.append(
                        {
                            "id": sub_shot_id,
                            "start": sub_start_time,
                            "end": sub_end_time,
                            "path": sub_clip_path,
                            "genre": shot_record.get("genre", "personal"),
                        }
                    )

        # 确认所有子片段都创建成功后，删除原始的长镜头记录和文件
        if len(new_sub_shots_info) == num_sub_shots:
            self.db_manager.delete_shot_record_by_id(shot_record["id"])
            original_clip_path.unlink(missing_ok=True)
            self.logger.info(f"已成功分割并替换原始长镜头 {original_clip_path.name}。")
            return new_sub_shots_info
        else:
            self.logger.error(f"未能成功分割所有子片段，原始长镜头 {original_clip_path.name} 将被保留。")
            # 清理可能已创建的不完整的子片段
            for sub_shot in new_sub_shots_info:
                Path(sub_shot["path"]).unlink(missing_ok=True)
                self.db_manager.delete_shot_record_by_id(sub_shot["id"])
            return []

    def _re_vectorize_shots(self) -> bool:
        """
        强制重新向量化模式的执行逻辑。
        此方法从数据库读取所有已分析的镜头，批量重新生成文本嵌入，并更新数据库记录。
        """
        from database.models import Shots
        from sqlalchemy.exc import ProgrammingError

        self.update_progress("开始为所有现有镜头重新生成文本向量...")
        with self.db_manager.get_session() as session:
            try:
                # 只查询重新向量化所必需的字段
                shots_to_process = (
                    session.query(Shots.id, Shots.analysis_data, Shots.refined_analysis_data)
                    .filter(Shots.video_id == self.video_id)
                    .all()
                )
            except ProgrammingError as e:
                # 捕获列不存在的特定数据库错误
                if "column" in str(e) and "does not exist" in str(e):
                    self.logger.error(
                        f"数据库表 'shots' 结构不完整，无法执行强制向量化。错误: {e}"
                        "请运行数据库迁移 (alembic upgrade head) 来同步数据库结构。"
                    )
                    return False
                else:
                    # 重新抛出其他未预料到的数据库错误
                    raise

            if not shots_to_process:
                self.logger.info("未找到任何镜头，无需进行向量化。")
                return True

            # 1. 收集所有需要处理的文本和对应的镜头ID
            texts_to_vectorize = []
            shot_ids_for_update = []
            total_shots = len(shots_to_process)
            self.update_progress(f"准备 {total_shots} 个镜头的描述文本...")

            for shot in shots_to_process:
                # 【核心修改】优先使用 refined_analysis_data，否则回退到 analysis_data
                prioritized_data = shot.refined_analysis_data or shot.analysis_data or {}
                descriptive_text = self._get_text_for_embedding(prioritized_data)
                if descriptive_text:
                    texts_to_vectorize.append(descriptive_text)
                    shot_ids_for_update.append(shot.id)

            if not texts_to_vectorize:
                self.logger.info("没有找到任何包含有效描述文本的镜头可供向量化。")
                return True

            # 2. 按批次循环调用嵌入模型 (此部分逻辑不变)
            total_texts = len(texts_to_vectorize)
            target_batch_size = settings.EMBEDDING_BATCH_SIZE
            batch_size = self._calculate_adaptive_batch_size(total_texts, target_batch_size)

            all_embedding_vectors = []
            num_batches = (total_texts + batch_size - 1) // batch_size

            for i in range(0, total_texts, batch_size):
                batch_texts = texts_to_vectorize[i : i + batch_size]
                batch_num = (i // batch_size) + 1
                self.update_progress(f"正在为 {total_texts} 条文本进行批量向量化 (批次 {batch_num}/{num_batches})...")

                embedding_vectors_batch = self.vision_client.get_embedding(batch_texts)

                if not embedding_vectors_batch or len(embedding_vectors_batch) != len(batch_texts):
                    self.logger.error(f"批量向量化失败 (批次 {batch_num}) 或返回的向量数量与输入不匹配。")
                    return False

                all_embedding_vectors.extend(embedding_vectors_batch)

            self.logger.info(f"批量向量化成功，共获得了 {len(all_embedding_vectors)} 个向量。")

            # 3. 准备批量更新 (此部分逻辑不变)
            updates = [
                {"id": shot_id, "text_embedding_vector": vector.tobytes()}
                for shot_id, vector in zip(shot_ids_for_update, all_embedding_vectors)
            ]

            # 4. 执行批量数据库更新 (此部分逻辑不变)
            if updates:
                self.update_progress(f"正在将 {len(updates)} 个更新后的向量写入数据库...")
                session.bulk_update_mappings(Shots.__mapper__, updates)
                self.logger.info(f"成功为 {len(updates)} 个镜头更新了文本向量。")
            else:
                self.logger.info("没有生成任何新的向量可供更新。")

        return True

    @property
    def stage_number(self) -> int:
        return 1

    @property
    def stage_name(self) -> str:
        return "镜头分析 (Shot Analysis)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行镜头分析"""
        self.force_level = force_level  # 设置实例属性以供其他方法使用

        if force_level == "vectorize":
            self.logger.info("--- 强制向量化模式 ---")
            return self._re_vectorize_shots()

        try:
            skip_until = kwargs.get("skip_until", 0.0)
            skip_after = kwargs.get("skip_after")  # 新增
            genre = kwargs.get("genre", "personal")

            run_shot_analysis = True
            if force_level:
                from database.models import Shots

                with self.db_manager.get_session() as session:
                    has_completed_shots = (
                        session.query(Shots.id)
                        .filter(Shots.video_id == self.video_id, Shots.status == "completed")
                        .first()
                        is not None
                    )
                if has_completed_shots:
                    self.logger.info(
                        f"强制模式 '{force_level}': 检测到已存在的镜头分析结果。将跳过镜头分析，仅重新提取人脸。"
                    )
                    run_shot_analysis = False

            if run_shot_analysis:
                self.update_progress("开始并行分析镜头内容")
                if not self._perform_shot_analysis(
                    force_level=force_level, skip_until=skip_until, skip_after=skip_after, genre=genre
                ):
                    return False

            if shutdown_event.is_set():
                return False

            if not self._perform_face_extraction_sequentially(skip_until=skip_until):
                return False

            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _process_single_shot(self, shot_info: Dict[str, Any], genre: str) -> bool:
        """处理单个已提取镜头的流程：生成低质版本、上传、视觉分析、向量化、保存"""
        shot_id, start_time, end_time, clip_path = (
            shot_info["id"],
            shot_info["start"],
            shot_info["end"],
            shot_info["path"],
        )

        try:
            # --- 新增：低分辨率文件验证与生成逻辑 ---
            try:
                expected_duration = video_processor.get_video_info(clip_path)["duration"]
            except Exception as e:
                raise RuntimeError(f"无法读取原始片段 {clip_path.name} 的信息: {e}")

            low_quality_video_path = clip_path.with_suffix(".lowres.mp4")
            is_lowres_valid = False

            if low_quality_video_path.exists():
                try:
                    actual_duration = video_processor.get_video_info(low_quality_video_path)["duration"]
                    if abs(actual_duration - expected_duration) < 0.1:
                        self.logger.debug(f"低分辨率文件 {low_quality_video_path.name} 已存在且时长有效。")
                        is_lowres_valid = True
                    else:
                        self.logger.warning(
                            f"低分辨率文件 {low_quality_video_path.name} 时长不匹配 "
                            f"(实际: {actual_duration:.2f}s, 预期: {expected_duration:.2f}s)。将重新生成。"
                        )
                except Exception as e:
                    self.logger.warning(
                        f"无法读取低分辨率文件 {low_quality_video_path.name} 的信息 (可能已损坏): {e}。将重新生成。"
                    )

            if not is_lowres_valid:
                self.logger.info(f"正在为 {clip_path.name} 创建/重新创建低分辨率版本...")
                if not video_processor.create_low_quality_video(
                    clip_path, low_quality_video_path, settings.LOW_QUALITY_BITRATE
                ):
                    raise RuntimeError("创建低质量视频失败")
            # --- 验证逻辑结束 ---

            low_quality_object_key = f"clips/low-res/{low_quality_video_path.name}"
            tos_client = get_tos_client()
            low_quality_clip_url = tos_client.upload_file(low_quality_video_path, low_quality_object_key)
            if not low_quality_clip_url:
                raise RuntimeError(f"上传低画质片段 {low_quality_video_path.name} 失败")

            # --- 【核心修改】构建统一的增强提示词 ---
            genre_instruction = GENRE_INSTRUCTIONS.get(genre, GENRE_INSTRUCTIONS["personal"])
            duration = end_time - start_time
            prompt = SCENE_ANALYSIS_PROMPT.format(duration=f"{duration:.2f}", genre_instruction=genre_instruction)
            # --- 修改结束 ---

            visual_analysis_result = self.vision_client.analyze_scene(
                video_url=low_quality_clip_url,
                prompt=prompt,  # 传递完整的、格式化好的提示词
            )
            self.logger.info(f"成功分析镜头 {start_time:.2f}s - {end_time:.2f}s")

            descriptive_text = self._get_text_for_embedding(visual_analysis_result)
            text_embedding_vector = self.vision_client.get_embedding(descriptive_text) if descriptive_text else None

            self._db_queue.put(
                (
                    self.db_manager.update_shot_analysis_result,
                    (shot_id, low_quality_clip_url, visual_analysis_result, text_embedding_vector),
                )
            )
            return True
        except Exception as e:
            self.logger.error(f"处理镜头ID {shot_id} 时出错: {e}")
            self._db_queue.put((self.db_manager.update_shot_error_status, (shot_id, str(e))))
            return False

    def _run_scenedetect_cli(
        self,
        input_video_path: Path,
        output_dir: Path,
        file_hash: str,
        video_height: int,
        skip_until: float = 0.0,
        skip_after: Optional[float] = None,
    ) -> Optional[Path]:
        """
        使用 scenedetect 命令行工具进行镜头检测，仅生成镜头列表CSV文件。
        """
        self.logger.info(f"开始使用 scenedetect CLI 对 '{input_video_path.name}' 进行镜头边界检测...")
        output_dir.mkdir(parents=True, exist_ok=True)

        cmd = ["scenedetect"]
        if video_height > 540:
            downscale_factor = max(2, round(video_height / 480))
            self.logger.info(f"视频高度为 {video_height}p，动态计算下采样因子为: {downscale_factor}")
            cmd.extend(["-d", str(downscale_factor)])

        cmd.extend(["-i", str(input_video_path)])

        time_args = []
        if skip_until > 0:
            self.logger.info(f"将跳过视频开头 {skip_until} 秒进行镜头检测。")
            time_args.extend(["--start", str(skip_until)])
        if skip_after is not None and skip_after > 0:
            self.logger.info(f"将跳过 {skip_after} 秒之后的所有内容进行镜头检测。")
            time_args.extend(["--end", str(skip_after)])

        if time_args:
            cmd.append("time")
            cmd.extend(time_args)

        # 【核心修改】移除 split-video 命令，只保留检测和列表生成
        detector = settings.SCENE_DETECTOR
        if detector == "adaptive":
            cmd.extend(["detect-adaptive"])
        elif detector == "content":
            cmd.extend(["detect-content"])
        elif detector == "threshold":
            cmd.extend(["detect-threshold", "-t", str(settings.SCENE_DETECTION_THRESHOLD)])
        else:
            self.logger.warning(f"无效的镜头检测器类型: '{detector}'。将回退到 'adaptive' 模式。")
            cmd.extend(["detect-adaptive"])

        csv_filename = f"{file_hash}-shots.csv"
        csv_full_path = output_dir / csv_filename
        cmd.extend(["list-scenes", "-f", str(csv_full_path)])

        try:
            self.logger.info(f"正在执行 scenedetect 命令: {' '.join(cmd)}")
            process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, encoding="utf-8", errors="ignore"
            )
            if process.stdout:
                for line in iter(process.stdout.readline, ""):
                    print(line.strip())
            return_code = process.wait()

            if return_code == 0:
                self.logger.info("scenedetect CLI 边界检测成功。")
                return csv_full_path if csv_full_path.exists() else None
            else:
                self.logger.error(f"scenedetect CLI 执行失败。返回码: {return_code}")
                return None
        except Exception as e:
            self.logger.error(f"执行 scenedetect CLI 时发生错误: {e}", exc_info=True)
            return None

    def _parse_scenedetect_csv(self, csv_path: Path) -> List[Tuple[float, float, int, int]]:
        """解析 scenedetect 生成的镜头列表CSV文件。"""
        shots = []
        try:
            with open(csv_path, "r", encoding="utf-8") as f:
                lines = f.readlines()
            csv_start_index = next((i for i, line in enumerate(lines) if line.strip().startswith("Scene Number,")), -1)
            if csv_start_index == -1:
                self.logger.error(f"在 {csv_path.name} 中未找到CSV表头 'Scene Number,'。")
                return []
            reader = csv.DictReader(lines[csv_start_index:])
            for row in reader:
                start_sec = float(row["Start Time (seconds)"])
                end_sec = float(row["End Time (seconds)"])
                start_frame = int(row["Start Frame"])
                end_frame = int(row["End Frame"])
                shots.append((start_sec, end_sec, start_frame, end_frame))
            self.logger.info(f"从 {csv_path.name} 中成功解析出 {len(shots)} 个镜头。")
            return shots
        except Exception as e:
            self.logger.error(f"解析镜头CSV文件 {csv_path} 时出错: {e}", exc_info=True)
            return []

    def _get_shot_status(self, start_time: float, end_time: float) -> Optional[str]:
        """获取单个镜头的状态"""
        from database.models import Shots

        with self.db_manager.get_session() as session:
            return (
                session.query(Shots.status)
                .filter_by(video_id=self.video_id, start_time=start_time, end_time=end_time)
                .scalar()
            )

    def _delete_shot_record(self, start_time: float, end_time: float):
        """删除指定的镜头记录"""
        from database.models import Shots

        with self.db_manager.get_session() as session:
            session.query(Shots).filter_by(video_id=self.video_id, start_time=start_time, end_time=end_time).delete()

    def _save_shot_analysis(
        self,
        start_time: float,
        end_time: float,
        clip_local_path: str,
        clip_file_size: int,
        low_quality_clip_url: str,
        visual_analysis: Dict[str, Any],
        text_embedding_vector: Optional[np.ndarray],
    ):
        """将镜头分析结果放入数据库写入队列"""
        args = (
            start_time,
            end_time,
            clip_local_path,
            clip_file_size,
            low_quality_clip_url,
            visual_analysis,
            text_embedding_vector,
        )
        self._db_queue.put((self._execute_save_shot_analysis, args))

    def _execute_save_shot_analysis(
        self,
        start_time: float,
        end_time: float,
        clip_local_path: str,
        clip_file_size: int,
        low_quality_clip_url: str,
        visual_analysis: Dict[str, Any],
        text_embedding_vector: Optional[np.ndarray],
    ):
        """实际执行保存镜头分析结果的数据库操作"""
        from database.models import Shots

        shot_analysis = visual_analysis.get("shot_analysis") or {}
        new_shot = Shots(
            video_id=self.video_id,
            start_time=start_time,
            end_time=end_time,
            clip_url=clip_local_path,
            low_quality_clip_url=low_quality_clip_url,
            clip_file_size=clip_file_size,
            visual_description=visual_analysis.get("visual_description", ""),
            people=visual_analysis.get("people", ""),
            setting=visual_analysis.get("setting", ""),
            action=visual_analysis.get("main_action", ""),
            emotion=visual_analysis.get("emotion", ""),
            key_objects=visual_analysis.get("key_objects", ""),
            shot_type=shot_analysis.get("shot_type", ""),
            camera_angle=shot_analysis.get("camera_angle", ""),
            camera_movement=shot_analysis.get("camera_movement", ""),
            composition=shot_analysis.get("composition", ""),
            lighting=visual_analysis.get("lighting", ""),
            action_intensity=visual_analysis.get("action_intensity"),
            subtext=visual_analysis.get("subtext"),
            comedy_type=visual_analysis.get("comedy_type"),
            on_screen_text=visual_analysis.get("on_screen_text", ""),
            text_embedding_vector=text_embedding_vector.tobytes() if text_embedding_vector is not None else None,
            status="completed",
        )
        with self.db_manager.get_session() as session:
            session.add(new_shot)

    def _save_shot_error(self, start_time: float, end_time: float, error_message: str, clip_url: Optional[str] = None):
        """将镜头分析错误放入数据库写入队列"""
        self._db_queue.put((self._execute_save_shot_error, (start_time, end_time, error_message, clip_url)))

    def _execute_save_shot_error(
        self, start_time: float, end_time: float, error_message: str, clip_url: Optional[str] = None
    ):
        """实际执行保存镜头分析错误的数据库操作"""
        from database.models import Shots

        new_shot_error = Shots(
            video_id=self.video_id,
            start_time=start_time,
            end_time=end_time,
            clip_url=clip_url,
            status="failed",
            error_message=error_message,
        )
        with self.db_manager.get_session() as session:
            session.add(new_shot_error)

    def _extract_face_embeddings(self, clip_path: Path) -> List[Dict[str, Any]]:
        """从视频片段中提取所有人脸的嵌入向量"""
        from utils.face_utils import get_face_embedder

        try:
            face_embedder = get_face_embedder()
            return face_embedder.extract_embeddings_from_clip(clip_path)
        except Exception as e:
            self.logger.error(f"调用 face_embedder 时发生未知异常: {e}", exc_info=True)
            return []

    def _perform_face_extraction_sequentially(self, skip_until: float = 0.0) -> bool:
        """串行执行人脸提取，并更新数据库记录。"""
        from database.models import FaceEmbeddings, Shots

        # 如果是强制模式，无论soft还是full，都应该清理旧的人脸数据。
        # 这对于跳过镜头分析、仅重做人脸提取的场景至关重要。
        if self.force_level:
            self.logger.info(f"强制模式 '{self.force_level}': 正在清理旧的人脸嵌入数据以便重新提取...")
            with self.db_manager.get_session() as session:
                subquery = session.query(Shots.id).filter(Shots.video_id == self.video_id).scalar_subquery()
                # 仅当存在关联的镜头时才执行删除，以避免空的IN子句问题
                if session.query(subquery.exists()).scalar():
                    deleted_count = (
                        session.query(FaceEmbeddings)
                        .filter(FaceEmbeddings.shot_id.in_(subquery))
                        .delete(synchronize_session=False)
                    )
                    self.logger.info(f"旧的人脸嵌入数据已清理 (删除了 {deleted_count} 条记录)。")
                else:
                    self.logger.info("未找到与此视频关联的镜头，无需清理人脸数据。")

        self.update_progress("开始串行提取人脸特征")

        with self.db_manager.get_session() as session:
            query = (
                session.query(Shots.id, Shots.clip_url, Shots.start_time)
                .filter(Shots.video_id == self.video_id, Shots.status == "completed", ~Shots.faces.any())
                .order_by(Shots.start_time)
            )
            if skip_until > 0:
                self.logger.info(f"人脸提取将跳过 {skip_until} 秒前的所有镜头。")
                query = query.filter(Shots.start_time >= skip_until)
            shots_to_process = query.all()

        if not shots_to_process:
            self.logger.info("没有需要进行人脸提取的新镜头。")
            return True

        total_shots = len(shots_to_process)
        self.logger.info(f"发现 {total_shots} 个需要提取人脸特征的新镜头，开始串行处理...")

        for i, (shot_id, clip_url, _) in enumerate(shots_to_process):
            if shutdown_event.is_set():
                self.logger.warning("检测到关闭信号，中断人脸提取。")
                return False
            self.update_progress(f"提取人脸: {i + 1}/{total_shots}")
            clip_path = Path(clip_url)
            if not clip_path.exists():
                self.logger.warning(f"找不到片段文件 {clip_path}，跳过镜头 {shot_id}。")
                continue
            face_embeddings = self._extract_face_embeddings(clip_path)
            if face_embeddings:
                batch_size = 20
                total_faces = len(face_embeddings)
                for j in range(0, total_faces, batch_size):
                    batch = face_embeddings[j : j + batch_size]
                    self.logger.debug(f"正在为镜头 {shot_id} 保存一批人脸数据 (数量: {len(batch)} / {total_faces})")
                    with self.db_manager.get_session() as update_session:
                        for face_data in batch:
                            new_face = FaceEmbeddings(
                                shot_id=shot_id,
                                embedding=face_data["embedding"].tobytes(),
                                bounding_box=face_data["bounding_box"],
                                time_offset_in_shot=face_data["time_offset"],
                            )
                            update_session.add(new_face)
        self.logger.info("所有人脸特征提取完成。")
        return True

    def _perform_shot_analysis(
        self,
        force_level: Optional[str] = None,
        skip_until: float = 0.0,
        skip_after: Optional[float] = None,
        genre: str = "personal",
    ) -> bool:
        """执行镜头检测、分割和分析的完整流程，并包含长镜头后处理。"""
        video_info = self.get_video_info()
        if not video_info or not video_info.get("input_file_path"):
            self.logger.error("视频信息不完整，无法执行分析。")
            return False

        input_video_path = Path(video_info["input_file_path"])
        clips_dir = settings.CLIPS_DIR
        file_hash = video_info["file_hash"]

        if force_level:
            self.logger.info(f"强制模式 '{force_level}': 正在清理旧的分析数据...")
            self.db_manager.clear_shot_analysis_data(self.video_id)
            if force_level == "full":
                self.logger.info("强制模式 'full': 清理所有相关缓存和文件...")
                for f in clips_dir.glob(f"{file_hash}-shot-*"):
                    f.unlink()
                (clips_dir / f"{file_hash}-shots.csv").unlink(missing_ok=True)

        # --- 步骤 1: 使用 scenedetect 进行初步的物理分割 ---
        shots_csv_path = clips_dir / f"{file_hash}-shots.csv"
        if not shots_csv_path.exists() or force_level == "full":
            self.logger.info("缓存未命中或强制执行，开始使用 scenedetect CLI 进行处理...")
            if not self._run_scenedetect_cli(
                input_video_path, clips_dir, file_hash, video_info.get("height", 1080), skip_until, skip_after
            ):
                return False

        parsed_shots = self._parse_scenedetect_csv(shots_csv_path)
        if not parsed_shots:
            return False

        # --- 新增：在解析CSV后立即根据 skip_until 和 skip_after 进行过滤 ---
        original_shot_count = len(parsed_shots)
        if skip_until > 0 or skip_after is not None:
            parsed_shots = [
                shot for shot in parsed_shots if shot[0] >= skip_until and (skip_after is None or shot[0] < skip_after)
            ]
            self.logger.info(
                f"已根据 --skip-until={skip_until:.2f}s 和 --skip-after={skip_after}s "
                f"将镜头列表从 {original_shot_count} 个过滤至 {len(parsed_shots)} 个。"
            )
        # --- 过滤结束 ---

        final_shots_for_db = []
        # --- 【核心修复】新增步骤: 在创建数据库记录前，物理提取所有检测到的镜头 ---
        self.update_progress(f"准备从主视频中提取 {len(parsed_shots)} 个镜头片段...")
        total_parsed = len(parsed_shots)
        for i, (start, end, _, _) in enumerate(parsed_shots):
            if shutdown_event.is_set():
                self.logger.warning("检测到关闭信号，中断镜头提取。")
                return False

            # 文件名从1开始编号，与 create_initial_shot_records 的逻辑保持一致
            clip_path = clips_dir / f"{file_hash}-shot-{i + 1:04d}.mp4"

            # --- 新增检查：验证是否存在完整且有效的子片段集 ---
            duration = end - start
            # 仅对可能被分割的长镜头进行子片段检查
            if duration > settings.MAX_SHOT_DURATION:
                num_expected_sub_shots = math.ceil(duration / settings.MAX_SHOT_DURATION)

                found_sub_clips = []
                all_subs_exist = True
                for sub_index in range(1, num_expected_sub_shots + 1):
                    # 只检查标准分辨率的mp4文件
                    expected_sub_path = clips_dir / f"{clip_path.stem}_sub_{sub_index}.mp4"
                    if expected_sub_path.exists():
                        found_sub_clips.append(expected_sub_path)
                    else:
                        all_subs_exist = False
                        break  # 只要有一个子片段缺失，就立即中断检查

                if all_subs_exist:
                    # 如果所有预期的子片段文件都存在，则进一步验证它们的总时长
                    try:
                        total_sub_duration = sum(video_processor.get_video_info(p)["duration"] for p in found_sub_clips)
                        # 使用一个小的容差（例如0.1秒）来比较浮点数时长
                        if abs(total_sub_duration - duration) < 0.1:
                            self.logger.info(
                                f"检测到完整且有效的子片段集 (共 {len(found_sub_clips)} 个)，"
                                f"将直接使用这些子片段，跳过提取原始长镜头 {clip_path.name}。"
                            )
                            # 【核心修改】将这些已存在的子片段信息添加到最终列表中
                            num_expected_sub_shots = math.ceil(duration / settings.MAX_SHOT_DURATION)
                            sub_shot_duration_segment = duration / num_expected_sub_shots
                            for sub_index in range(num_expected_sub_shots):
                                sub_start = round(start + sub_index * sub_shot_duration_segment, 3)
                                sub_end = round(start + (sub_index + 1) * sub_shot_duration_segment, 3)
                                if sub_index == num_expected_sub_shots - 1:
                                    sub_end = end  # 避免浮点误差
                                sub_path = clips_dir / f"{clip_path.stem}_sub_{sub_index + 1}.mp4"
                                final_shots_for_db.append((sub_start, sub_end, str(sub_path.resolve())))
                            continue  # 跳到主循环的下一次迭代
                        else:
                            self.logger.warning(
                                f"子片段集存在但总时长 ({total_sub_duration:.2f}s) 与原始镜头 "
                                f"({duration:.2f}s) 不匹配。将重新提取原始镜头 {clip_path.name}。"
                            )
                    except Exception as e:
                        self.logger.warning(f"检查子片段时长时出错: {e}。将重新提取原始镜头 {clip_path.name}。")
            # --- 检查结束 ---

            self.update_progress(f"提取镜头片段: {clip_path.name} ({i + 1}/{total_parsed})")

            # 如果文件已存在，则在跳过前验证其时长
            if clip_path.exists():
                try:
                    # 从已存在的文件获取实际时长
                    actual_duration = video_processor.get_video_info(clip_path)["duration"]
                    # 从CSV解析数据中获取预期时长
                    expected_duration = end - start

                    # 比较时长，允许一个小的容差
                    if abs(actual_duration - expected_duration) < 0.1:
                        self.logger.debug(f"片段 {clip_path.name} 已存在且时长有效，跳过提取。")
                        final_shots_for_db.append((start, end, str(clip_path.resolve())))
                        continue  # 验证通过，跳到下一个循环
                    else:
                        self.logger.warning(
                            f"片段 {clip_path.name} 已存在但时长不匹配 "
                            f"(实际: {actual_duration:.2f}s, 预期: {expected_duration:.2f}s)。将重新提取。"
                        )
                except Exception as e:
                    self.logger.warning(f"无法获取已存在片段 {clip_path.name} 的信息 (可能已损坏): {e}。将重新提取。")
                # 如果时长不匹配或获取信息失败，代码将不会执行 continue，而是会继续执行下面的提取逻辑

            # 从原始视频文件中，根据绝对时间戳提取片段
            if not video_processor.extract_clip(input_video_path, start, end, clip_path):
                self.logger.error(f"无法提取镜头片段 {clip_path.name} (时间: {start:.2f}s - {end:.2f}s)。")
                # 记录错误并继续，以避免单个提取失败导致整个流程中断
                continue

            final_shots_for_db.append((start, end, str(clip_path.resolve())))
        self.logger.info("所有初步检测到的镜头片段已提取完成。")
        # --- 修复结束 ---

        # --- 步骤 2: 将最终确认的镜头列表存入数据库 ---
        self.db_manager.create_initial_shot_records(self.video_id, final_shots_for_db)

        # --- 步骤 3: 【核心修改】处理过长的镜头 ---
        self.update_progress("检查并物理分割过长的镜头片段...")
        fps = video_info.get("fps")
        if not fps or fps <= 0:
            self.logger.error("无法获取有效的视频FPS，跳过长镜头分割。")
            return False

        long_shots = self.db_manager.get_long_shots(self.video_id, threshold=settings.MAX_SHOT_DURATION)
        newly_created_sub_shots = []
        for long_shot in long_shots:
            sub_shots = self._split_and_update_long_shot(long_shot, fps)
            newly_created_sub_shots.extend(sub_shots)

        # --- 步骤 4: 准备并执行AI分析 ---
        shots_to_analyze = self.db_manager.get_shots_to_analyze(self.video_id, force_level)
        if not shots_to_analyze:
            self.logger.info("没有需要进行AI分析的新镜头。")
            return True

        # 将新创建的子镜头也加入分析队列
        shots_to_analyze.extend(newly_created_sub_shots)

        # 去重，以防万一
        unique_shots_to_analyze = {s["id"]: s for s in shots_to_analyze}.values()

        self.logger.info(f"共准备好 {len(unique_shots_to_analyze)} 个镜头进行AI分析。")
        completed_count, failed_count = 0, 0
        db_writer_thread = threading.Thread(target=self._db_writer_worker, daemon=True)
        db_writer_thread.start()
        try:
            # --- 新增：渐进式并发控制逻辑 ---
            tasks = list(unique_shots_to_analyze)
            total_tasks = len(tasks)
            task_iterator = iter(tasks)
            active_futures = set()

            with concurrent.futures.ThreadPoolExecutor(max_workers=settings.MAX_CONCURRENT_REQUESTS) as executor:
                while completed_count + failed_count < total_tasks:
                    if shutdown_event.is_set():
                        self.logger.warning("检测到关闭信号，停止提交新任务。")
                        # 等待已提交的任务完成
                        for future in list(active_futures):
                            future.cancel()
                        break

                    # 1. 逐步增加并发任务
                    # 计算本轮可以提交多少新任务
                    can_add_count = settings.CONCURRENCY_RAMP_UP_BATCH_SIZE
                    current_capacity = settings.MAX_CONCURRENT_REQUESTS - len(active_futures)
                    num_to_submit = min(can_add_count, current_capacity)

                    for _ in range(num_to_submit):
                        try:
                            task = next(task_iterator)
                            future = executor.submit(self._process_single_shot, task, genre)
                            active_futures.add(future)
                        except StopIteration:
                            # 所有任务都已提交
                            break

                    # 如果没有正在执行的任务，且还有任务未完成，则说明所有任务都已提交完毕
                    if not active_futures:
                        break

                    # 2. 等待并处理已完成的任务
                    # as_completed 会在至少一个任务完成后返回
                    for future in concurrent.futures.as_completed(active_futures):
                        try:
                            if future.result():
                                completed_count += 1
                            else:
                                failed_count += 1
                        except Exception:
                            failed_count += 1
                        self.update_progress(f"分析中 - {completed_count + failed_count}/{total_tasks}")

                        # 从活跃集合中移除已完成的任务
                        active_futures.remove(future)
                        # 处理完一个就跳出内层循环，回到外层循环去检查是否可以提交新任务
                        break
            # --- 逻辑结束 ---
        finally:
            self._db_queue.join()
            self._stop_db_writer.set()
            db_writer_thread.join()

        self.logger.info(f"镜头分析完成。成功: {completed_count}，失败: {failed_count}")

        # --- 新增：在所有镜头操作完成后，全局重新编号 shot_order_id ---
        if failed_count == 0 and not shutdown_event.is_set():
            self.update_progress("正在为所有镜头重新生成顺序ID...")
            if not self.db_manager.renumber_shot_order_ids(self.video_id):
                self.logger.error("为镜头重新编号 shot_order_id 失败，阶段1分析中止。")
                return False
            self.logger.info("所有镜头的 shot_order_id 已成功更新并保持连续。")
        # --- 新增结束 ---

        return failed_count == 0 and not shutdown_event.is_set()

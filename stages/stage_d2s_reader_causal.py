"""
阶段13：D2S 读者模块 (Reader)
第二步：因果链接推断
"""

import json
from typing import Any, Dict, Optional

from config.prompts import CAUSAL_LINK_INFERENCE_PROMPT, CausalLinkInferenceResponse
from config.schemas import ModelName
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import volcengine_client
from utils.enhanced_base_stage import EnhancedBaseStage


class Stage14ReaderCausalLink(BaseStage, EnhancedBaseStage):
    """阶段14：D2S 读者模块 - 因果链接推断"""

    def __init__(self, db_manager, video_id):
        BaseStage.__init__(self, db_manager, video_id)
        EnhancedBaseStage.__init__(self, db_manager, video_id)
        # 此阶段的核心是AI解析，可以使用高级AI客户端
        self.reader_client = volcengine_client
        self.reader_client.model = ModelName.DOUBAO_SEED_1_6.value  # 已修改

    @property
    def stage_number(self) -> int:
        return 14

    @property
    def stage_name(self) -> str:
        return "D2S Reader (因果推断)"

    def _format_dossier_for_prompt(self, dossier_data: Dict[str, Any]) -> str:
        """将JSON格式的角色档案转换为格式化的纯文本，用于提示词。"""
        if not dossier_data or "dossiers" not in dossier_data:
            return "无角色档案。"

        parts = []
        for dossier in dossier_data["dossiers"]:
            parts.append(
                f"character_id: {dossier.get('character_id')}\n"
                f"name: {dossier.get('name')}\n"
                f"background: {dossier.get('background')}\n"
                f"motivation: {dossier.get('motivation')}\n"
                f"conflict: {dossier.get('conflict')}\n"
                f"arc: {dossier.get('arc')}"
            )
        return "\n\n".join(parts)

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage13_status = self.db_manager.get_stage_status(self.video_id, 13)
        if not stage13_status or stage13_status["status"] != "completed":
            return False, "阶段13 (D2S Reader 事件识别) 尚未完成"

        events_data = self.db_manager.get_stage_output(self.video_id, 13, "narrative_events")
        if not events_data:
            return False, "数据库中未找到叙事事件列表数据"

        if not self.db_manager.get_all_research_summaries(self.video_id):
            return False, "数据库中未找到研究资料摘要"

        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行阶段处理，使用增强功能"""
        return self.execute_with_enhancement(force_level, **kwargs)

    def _execute_enhanced(self, cross_stage_context: Dict, force_level: Optional[str] = None, **kwargs) -> bool:
        """带跨阶段上下文的增强执行方法。"""
        if force_level:
            self.logger.info(f"强制模式 ({force_level}): 清理旧的因果图谱数据。")
            self.db_manager.clear_stage_output(self.video_id, self.stage_number, "causal_graph")

        existing_output = self.db_manager.get_stage_output(self.video_id, self.stage_number, "causal_graph")
        if existing_output and not force_level:
            self.logger.info("从数据库加载已缓存的因果图谱数据。")
            return True

        try:
            creative_brief = self.db_manager.get_stage_output(self.video_id, 11, "creative_brief")
            events_data = self.db_manager.get_stage_output(self.video_id, 13, "narrative_events")
            narrative_events = events_data.get("narrative_events", []) if events_data else []
            research_summary = self.db_manager.get_all_research_summaries(self.video_id)
            dossier_data = self.db_manager.get_stage_output(self.video_id, 12, "character_dossier") or {}
            dossier_text = self._format_dossier_for_prompt(dossier_data)

            if not creative_brief or not narrative_events:
                self.logger.error("无法从数据库加载前置阶段的输出数据或叙事事件列表为空。")
                return False
        except Exception as e:
            self.logger.error(f"无法加载前置数据: {e}")
            return False

        self.update_progress("AI正在推断事件间的因果关系...")
        try:
            prompt = CAUSAL_LINK_INFERENCE_PROMPT.format(
                research_summary=research_summary,
                project_info_json=json.dumps(creative_brief.get("project_info"), ensure_ascii=False, indent=2),
                dossier_text=dossier_text,
                narrative_events_json=json.dumps(narrative_events, ensure_ascii=False, indent=2),
                language=settings.SCRIPT_LANGUAGE,
            )

            # 添加重试指导
            retry_guidance = cross_stage_context.get("retry_guidance", {})
            if retry_guidance:
                suggestions = retry_guidance.get("improvement_suggestions", [])
                if suggestions:
                    prompt += "\n\n# 重试改进指导\n请特别注意以下方面：\n" + "\n".join(f"- {s}" for s in suggestions)

            result = self.reader_client.call_ai_with_tool(prompt, response_model=CausalLinkInferenceResponse)

            if not result:
                self.logger.error("AI未能返回任何因果链接推断结果。")
                return False

            causal_graph = {"nodes": narrative_events, "edges": result.model_dump().get("causal_links", [])}
            self.db_manager.save_stage_output(
                video_id=self.video_id,
                stage_number=self.stage_number,
                stage_name=self.stage_name,
                output_type="causal_graph",
                output_data=causal_graph,
            )

            self.logger.info(f"✅ 成功推断出 {len(causal_graph['edges'])} 条因果链接并保存到数据库。")
            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def get_stage_output(self) -> Dict:
        """获取阶段输出数据，用于质量评估。"""
        return self.db_manager.get_stage_output(self.video_id, self.stage_number, "causal_graph") or {}

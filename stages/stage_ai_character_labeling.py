"""
阶段9：AI 角色标注 (AI Character Labeling)
利用多模态AI和叙事上下文，对未识别的角色进行智能标注。
"""

import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional

from config.prompts.character_labeling import (
    AILabelingResponse,
    CHARACTER_AI_LABELING_PROMPT,
)
from config.schemas import ModelName
from stages.base import BaseStage
from utils.ai_utils import primary_client
from utils.storage_utils import get_tos_client
from utils.video_utils import video_processor


class Stage9AICharacterLabeling(BaseStage):
    """阶段9：AI 角色标注"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        self.labeling_client = primary_client
        self.labeling_client.model = ModelName.DOUBAO_SEED_1_6_THINKING.value

    @property
    def stage_number(self) -> int:
        return 9

    @property
    def stage_name(self) -> str:
        return "AI 角色标注 (AI Character Labeling)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件：序列分析必须完成"""
        stage8_status = self.db_manager.get_stage_status(self.video_id, 8)
        if not stage8_status or stage8_status["status"] != "completed":
            return False, "阶段8 (序列分析) 尚未完成"
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行AI角色标注流程"""
        if force_level:
            self.logger.info("强制模式: 将重新进行所有AI角色标注。")
            self._reset_ai_labeled_characters()

        # 1. 获取所有需要AI标注的角色
        characters_to_label = self.db_manager.get_characters_for_ai_labeling(self.video_id)
        if not characters_to_label:
            self.logger.info("没有需要进行AI标注的角色。")
            return True

        # 2. 获取已命名的角色作为上下文
        known_characters = self.db_manager.get_all_named_characters(self.video_id)
        known_characters_list = [char["name"] for char in known_characters]

        self.update_progress(f"准备为 {len(characters_to_label)} 个未知角色进行AI标注...")

        # 3. 迭代处理每个需要标注的角色
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            for char_info in characters_to_label:
                self._label_single_character(char_info, known_characters_list, temp_path)

        self.logger.info("AI角色标注流程完成。")
        return True

    def _reset_ai_labeled_characters(self):
        """将之前由AI标注的角色重置回“未知”状态"""
        self.db_manager.reset_characters_by_source(self.video_id, "ai_labeled")

    def _label_single_character(self, char_info: Dict[str, Any], known_characters_list: List[str], temp_dir: Path):
        """使用AI标注单个角色"""
        char_id = char_info["character_id"]
        self.update_progress(f"正在为角色ID {char_id} 寻找最佳识别样本...")

        # 1. 准备图片和上下文
        face_info = self.db_manager.get_best_face_for_character(char_id)
        if not face_info:
            self.logger.warning(f"无法为角色ID {char_id} 找到代表性人脸，跳过标注。")
            return

        face_image_path = temp_dir / f"char_{char_id}_face.jpg"
        video_path = Path(face_info["clip_url"])

        if not video_path.exists():
            self.logger.error(f"源视频片段丢失，无法为角色ID {char_id} 提取人脸图像: {video_path}")
            return

        video_processor.extract_and_save_face_image(
            video_path=video_path,
            time_seconds=face_info["time_offset_in_shot"],
            bounding_box=face_info["bounding_box"],
            output_path=face_image_path,
        )

        # 2. 上传图片至对象存储
        tos_client = get_tos_client()
        object_key = f"temp_faces_for_labeling/{self.video_id}/{face_image_path.name}"
        image_url = tos_client.upload_file(face_image_path, object_key)
        if not image_url:
            self.logger.error(f"上传角色 {char_id} 的人脸图片失败。")
            return

        # 3. 调用多模态AI进行识别
        prompt = CHARACTER_AI_LABELING_PROMPT.format(
            known_characters_list="\n".join([f"- {name}" for name in known_characters_list]) or "无",
            sequence_theme=face_info.get("sequence_theme", "未知"),
            scene_summary=face_info.get("scene_summary", "未知"),
            shot_description=face_info.get("visual_description", "无"),
            shot_dialogue=face_info.get("dialogue", "无"),
        )

        try:
            multi_part_content = [
                {"type": "image_url", "image_url": {"url": image_url}},
                {"type": "text", "text": prompt},
            ]
            response = self.labeling_client.call_ai_with_tool(multi_part_content, response_model=AILabelingResponse)

            if response:
                new_name = response.character_name
                self.logger.info(f"AI标注结果 -> 角色ID {char_id}: '{new_name}'. 理由: {response.justification}")
                self.db_manager.update_character_name(char_id, new_name, source="ai_labeled")
            else:
                self.logger.warning(f"AI未能为角色ID {char_id} 提供有效标注。")

        except Exception as e:
            self.logger.error(f"为角色ID {char_id} 调用AI标注时失败: {e}", exc_info=True)

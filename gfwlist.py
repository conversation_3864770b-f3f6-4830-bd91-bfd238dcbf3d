#!/usr/bin/python3

import os
import base64
import json
import requests


GFWLIST_FILE = "gfwlist.txt"
GFWLIST_URL = 'https://raw.githubusercontent.com/gfwlist/gfwlist/master/gfwlist.txt'

DIRECT_DOMAINS_FILE = "direct-domains.txt"
PROXY_DOMAINS_FILE = "proxy-domains.txt"


def get_gfwlist():
    if os.path.isfile(GFWLIST_FILE):
        with open(GFWLIST_FILE, "r") as f:
            text = f.read()
    else:
        r = requests.get(GFWLIST_URL)
        r.raise_for_status()
        text = r.text
    return base64.b64decode(text).decode("utf-8").rstrip("\n")


def update_domains(domains, host, mode=0):
    segments = host.strip(".").split(".")[::-1]

    this = domains
    for segment in segments:
        if segment not in this:
            this[segment] = {}
        this = this[segment]
    this["@"] = mode


def postproc_domains(domains):
    # Turn all {"@": 1} into 1 to save some text
    keys = list(domains.keys())
    for key in keys:
        if key == "@":
            continue
        obj = domains[key]
        if isinstance(obj, dict):
            if len(obj) == 1 and "@" in obj:
                domains[key] = obj["@"]
            else:
                postproc_domains(obj)


def parse_gfwlist(text):
    domains = {}
    blackpat = []  # blacklisted patterns
    whitepat = []  # whitelisted patterns

    for line in text.splitlines()[1:]:
        if not line.strip() or line.startswith("!"):
            continue  # ignore comments and empty lines

        mode = 0  # default to blacklist
        if line.startswith("@@"):
            mode = 1  # now it's whitelist
            line = line[2:]

        if line.startswith("||"):
            # domain prefix
            update_domains(domains, line[2:], mode)
        elif line.startswith("/"):
            # regex, can't handle yet
            pass
        else:
            # Keyword pattern
            # Single vertical line at either side means string boundary
            if line.startswith("|"):
                line = line[1:]
            else:
                line = "*" + line
            if line.endswith("|"):
                line = line[:-1]
            else:
                line = line + "*"
            if mode == 0:
                blackpat.append(line)
            else:
                whitepat.append(line)
    postproc_domains(domains)
    return domains, blackpat, whitepat


def read_local_domains(filename, mode=0):
    """Read local domain list from file and return domains dict."""
    domains = {}
    if not os.path.isfile(filename):
        return domains
    
    with open(filename, "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith("#"):
                continue  # Skip empty lines and comments
            
            # Clean domain name
            domain = line.lower().strip()
            if not domain:
                continue
                
            # Handle wildcard patterns
            if domain.startswith("*."):
                # Wildcard pattern like *.example.com - use subdomain matching
                clean_domain = domain[2:]  # Remove "*."
                update_domains(domains, clean_domain, mode)
            else:
                # Exact domain - mark only this specific domain
                segments = domain.strip(".").split(".")
                if len(segments) >= 2:  # Valid domain format
                    # Add @ marker only at the exact domain level
                    current = domains
                    for segment in segments[::-1]:  # Reverse order for tree structure
                        if segment not in current:
                            current[segment] = {}
                        elif not isinstance(current[segment], dict):
                            # Convert existing value to dict if needed
                            old_value = current[segment]
                            current[segment] = {"@": old_value}
                        current = current[segment]
                    # Only set @ at the exact domain level, no subdomain propagation
                    current["@"] = mode
    
    return domains


def merge_domains(gfwlist_domains, local_domains):
    """Merge local domains into GFWList domains, local takes precedence."""
    merged = gfwlist_domains.copy()
    
    def merge_recursive(target, source):
        for key, value in source.items():
            if key == "@":
                # Local domains override GFWList settings
                target[key] = value
            elif isinstance(value, dict):
                if key not in target:
                    target[key] = {}
                elif not isinstance(target[key], dict):
                    # Convert existing value to dict if needed
                    old_value = target[key]
                    target[key] = {"@": old_value}
                merge_recursive(target[key], value)
            else:
                # Direct value assignment
                target[key] = value
    
    merge_recursive(merged, local_domains)
    return merged


def generate_pac_partial():
    gfwlist = get_gfwlist()
    domains, blackpat, whitepat = parse_gfwlist(gfwlist)
    
    # Read local domain lists
    direct_domains = read_local_domains(DIRECT_DOMAINS_FILE, mode=1)  # 1 = whitelist/direct
    proxy_domains = read_local_domains(PROXY_DOMAINS_FILE, mode=0)   # 0 = blacklist/proxy
    
    # Merge local domains with GFWList domains
    all_domains = merge_domains(domains, direct_domains)
    all_domains = merge_domains(all_domains, proxy_domains)
    
    postproc_domains(all_domains)
    
    return "var DOMAINS = {};\n\nvar BLACKPAT = {};\n\nvar WHITEPAT = {};\n".format(
        json.dumps(all_domains, indent=2),
        json.dumps(blackpat, indent=2),
        json.dumps(whitepat, indent=2),
    )


if __name__ == '__main__':
    print(generate_pac_partial())

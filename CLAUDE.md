# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a PAC (Proxy Auto-Configuration) script generator that creates whitelist-based proxy configuration files. It generates two types of PAC files:
1. IP-based whitelist (China IP ranges)
2. GFWList-enhanced versions (combining IP whitelist with domain blacklist)

## Architecture

### Core Components

- **build.py**: Main build script that fetches IP ranges and generates PAC files
- **gfwlist.py**: Handles GFWList (Great Firewall list) processing and domain matching
- **code.js**: PAC script template with JavaScript logic for proxy decision-making
- **release-info.py**: GitHub Actions helper for automated releases

### Data Flow
1. IP ranges fetched from external sources (ipdeny.com, 17mon)
2. GFWList domains fetched from GitHub repository
3. Python processes data into JavaScript arrays
4. Templates assembled into final PAC files
5. Automated weekly builds via GitHub Actions

## Key Technical Details

### IP Processing
- IPv4 networks converted from CIDR notation to [network, netmask] pairs
- Binary search used for efficient IP range matching in JavaScript
- LAN IP ranges predefined for local network bypass

### Domain Processing
- GFWList parsed into hierarchical domain tree structure
- Regex patterns handled separately from domain matching
- Whitelist/blacklist precedence: URL patterns → domain rules → IP rules

### Build System
- **Command**: `python3 build.py`
- **Output**: `dist/pac-{source}.txt` and `dist/pac-gfwlist-{source}.txt`
- **CI/CD**: GitHub Actions workflow runs weekly on Saturdays at 12:00 UTC
- **Release**: Automatic tagging and release creation

### Dependencies
- Python 3.x
- `requests` library for HTTP fetching
- Standard libraries: `ipaddress`, `base64`, `json`, `urllib.parse`

## Development Commands

### Local Development
```bash
# Generate PAC files locally
python3 build.py

# Test GFWList processing
python3 gfwlist.py

# Generate release info (GitHub Actions context)
python3 release-info.py
```

### Manual Testing
- PAC files output to `dist/` directory
- Files are gzip-compressed in CI (use `gzip -d` to decompress)
- Test PAC functionality by setting browser proxy to generated file

## File Structure

```
gfw2pac/
├── build.py          # Main build orchestrator
├── gfwlist.py        # GFWList parser and generator
├── code.js           # PAC template with proxy logic
├── release-info.py   # GitHub Actions release helper
├── .github/workflows/build.yml  # CI/CD pipeline
└── dist/             # Generated PAC files (created during build)
```

## Configuration Points

### Data Sources
- `SOURCES` dict in build.py defines IP range providers
- GFWList URL hardcoded in gfwlist.py
- LAN ranges defined in code.js (lines 97-105)

### PAC Customization
- Line 5 in code.js: Replace `__PROXY__` with actual proxy address for manual use
- Proxy format: typically `PROXY host:port` or `SOCKS5 host:port`

## Integration Notes

### For Shadowsocks Windows
- Designed for Shadowsocks Windows 4.1.9+ (auto-replaces `__PROXY__`)
- For older versions: manually edit line 5 with proxy address

### For Other Proxy Clients
- Modify `__PROXY__` placeholder in generated PAC files
- Ensure JavaScript compatibility with target proxy client

## Performance Considerations

- Binary search on sorted IP ranges for O(log n) lookup
- Domain tree structure for efficient domain matching
- Minimal PAC file size through compact data representation
- IPv6 explicitly not supported (see README.md for IPv6 fork)
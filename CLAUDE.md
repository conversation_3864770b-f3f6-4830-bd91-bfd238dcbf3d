# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Commands

### Running the System
```bash
# Import new videos from input directory
./main.py import

# List all videos and their processing status
./main.py list

# Check status of specific video (use -v flag for verbose output)
./main.py status -v 1

# Run specific stage for a video
./main.py run --video 1 --stage 1

# Run all stages from a specific starting point
./main.py run --video 1 --stage all --from 3

# Force modes for reprocessing
./main.py run -v 1 -s 1 --force full    # Complete reprocessing (deletes all cache/clips)
./main.py run -v 1 -s 1 --force soft    # Preserve split clips, redo AI analysis
./main.py run -v 1 -s 1 --force vectorize  # Only regenerate text vectors
```

### Interactive Commands
```bash
# Character naming interface (after stage 6)  
./main.py name -v 1

# Review causal graph (after stage 13)
./main.py review_graph -v 1

# Interactive script refinement (after stage 17+)
./main.py refine_script -v 1

# Semantic search in processed footage
./main.py search -v 1 -q "a man running in rain" -k 3
```

### Database Management
```bash
# Run database migrations
alembic upgrade head

# Create new migration
alembic revision --autogenerate -m "description"

# Check migration status
alembic current
```

### Development & Testing
```bash
# Type checking
pyright

# Linting (configured in ruff.toml)
ruff check .
ruff format .

# Run specific test
python -m pytest tests/test_volc.py -v
```

## High-Level Architecture

### Core Pipeline: Data-to-Script (D2S) Workflow

This system implements a sophisticated "Snowflake Method" inspired workflow that transforms raw video assets into polished narrative scripts through three major phases:

#### Phase 1: Asset Deconstruction & Data Foundation (Stages 1-10)
- **Stages 1-9**: Multi-layered video analysis including shot detection, visual analysis, audio transcription, character identification, and scene aggregation
- **Stage 10**: AI generates a comprehensive "Creative Brief" serving as the single source of truth for all subsequent AI operations

#### Phase 2: Narrative Core Generation (Stages 11-21) 
Uses advanced "Reader-Rewriter" paradigm:

**Reader Module (Stages 11-13):**
- Stage 11: Event identification from material
- Stage 12: Causal relationship inference  
- Stage 13: Graph refinement for narrative consistency

**Rewriter Module (Stages 14-21):**
- Stage 14: Detailed scene-by-scene story outline
- Stage 15: Character dossier creation
- Stage 16: Creative strategy planning
- Stage 17: Story flow arrangement with dramatic tension
- Stage 18: Full script creation with action, dialogue, and narration
- Stage 19: AI shot selection and editing refinement
- Stage 20: Duration control and optimization
- Stage 21: Final production with TTS synthesis and FCP7 XML export

#### Phase 3: Human-AI Collaboration & Quality Control
- Integrated quality assessment using REACT-S framework
- Interactive commands for reviewing and refining AI decisions
- Structured feedback loops between AI efficiency and human creativity

### Key Architectural Components

#### Database-Driven Design
- **PostgreSQL + SQLAlchemy**: All structured data stored in database for fast retrieval
- **Alembic migrations**: Schema versioning and evolution
- **Models in database/models.py**: Core data structures for videos, shots, scenes, scripts, characters

#### Modular Stage System
- **Base class**: `stages/base.py` defines common interface for all processing stages
- **Stage discovery**: Automatic loading of stage_*.py files with dependency checking
- **Prerequisites**: Each stage declares required predecessor stages
- **Force modes**: Flexible reprocessing options (full/soft/vectorize)

#### AI Integration Layer
- **Multiple AI clients**: `utils/ai_utils.py` provides different capability levels
- **Structured outputs**: Pydantic schemas in `config/schemas.py` ensure consistent AI responses  
- **Modular prompt system**: Prompts organized by functionality in `config/prompts/` directory
  - Each stage's prompts isolated in dedicated modules
  - Common prompt building functions with shared principles
  - Centralized import system through `__init__.py`
- **Retry mechanisms**: Robust error handling for API calls
- **Cost optimization**: Intelligent model selection based on task complexity

#### Processing Infrastructure
- **Concurrent processing**: ThreadPoolExecutor for parallel scene analysis
- **Video processing**: OpenCV + PySceneDetect for shot detection and analysis
- **Audio pipeline**: Demucs for voice separation, MLX-Whisper for transcription
- **Face recognition**: FaceNet-PyTorch with clustering for character identification
- **Vector search**: Embeddings-based semantic search across video content

#### Storage & Asset Management
- **Clips directory**: Organized video segments by hash + shot number
- **Output directory**: JSON stage outputs, audio files, debug artifacts
- **Temp directories**: Intermediate processing files
- **Asset organization**: Consistent naming schemes for traceability

### Configuration & Settings

#### Environment Variables (.env)
- Database connection parameters (POSTGRES_*)
- AI API keys and endpoints (VOLC_API_*, OPENAI_*)
- Processing limits (MAX_CONCURRENT_REQUESTS, DEBUG_MODE_SCENE_LIMIT)
- TTS configuration (EDGE_TTS_VOICE, NARRATION_CHAR_PER_SEC)

#### Key Configuration Files
- `config/settings.py`: Central configuration management
- `config/prompts/`: Modular AI prompt templates organized by functionality
  - `config/prompts/__init__.py`: Central import hub for all prompts and schemas
  - `config/prompts/base.py`: Common prompt building functions (`solver_prompt`, `verifier_prompt`)
  - Individual modules: `scene_analysis.py`, `script_writing.py`, `editing.py`, etc.
- `config/schemas.py`: Pydantic response models for structured AI outputs
- `pyproject.toml`: Dependencies and project metadata
- `ruff.toml`: Code formatting rules (line-length: 120)

### Important Development Patterns

#### Stage Implementation Requirements
- All stages inherit from `BaseStage` 
- Must implement: `stage_number`, `stage_name`, `check_prerequisites()`, `execute()`
- Stage numbers determined by class property, not filename
- Dependencies declared in `check_prerequisites()`

#### Error Handling & Logging
- Centralized logging via `utils/logger.py`
- Structured error messages with context
- Progress tracking with `update_progress()` method
- Graceful degradation for non-critical failures

#### Force Mode Semantics
- **full**: Complete reprocessing, delete all artifacts
- **soft**: Preserve expensive preprocessing (clips), redo analysis
- **vectorize**: Only regenerate embeddings/vectors
- Stage-specific cleanup in force mode handlers

#### AI Client Selection Strategy
- `basic_ai_client`: Simple tasks, cost-effective
- `advanced_ai_client`: Complex reasoning, structured outputs
- `most_advanced_ai_client`: Critical creative decisions (Stage 19 refinement)

#### Prompt System Architecture
- **Modular Design**: Each functional area has dedicated prompt modules (e.g., `scene_analysis.py`, `script_writing.py`)
- **Common Principles**: `solver_prompt()` and `verifier_prompt()` functions apply consistent guidelines
- **Structured Imports**: All prompts and schemas accessible through `config.prompts` package
- **Template Pattern**: Prompts use consistent structure with role definition, input specification, and task rules
- **Schema Coupling**: Each prompt paired with corresponding Pydantic response model for type safety

### Testing & Quality Assurance

#### Current Test Coverage
- `tests/test_volc.py`: API integration testing
- Manual testing via interactive commands
- Stage-by-stage validation through status checking

#### Debugging Tools
- `scripts/debug_vector_search.py`: Vector similarity testing
- Debug output directory for intermediate results
- Verbose logging modes for troubleshooting
- Interactive graph review for causal logic validation

### Performance Considerations

#### Hardware Requirements
- **GPU recommended**: Demucs, FaceNet-PyTorch benefit significantly
- **Apple Silicon**: MLX-Whisper optimized for MPS acceleration
- **Storage**: Substantial disk space needed for video clips and intermediate files

#### Scalability Patterns
- Concurrent processing with configurable thread limits
- Database connection pooling
- Smart caching of expensive operations (face recognition, transcription)
- Incremental processing - only recompute what's needed

## Special Notes on Advanced Stages

### Editing and Refinement Pipeline (Stages 19-21)

The final stages represent sophisticated post-processing that transforms script content into production-ready material:

**Stage 19 (AI Shot Selection):**
- Intelligent selection of optimal shots from available footage
- Content-aware matching between script descriptions and visual material
- Maintains narrative flow while optimizing visual storytelling

**Stage 20 (Duration Control):**
- Automated time optimization to meet platform requirements
- Intelligent content trimming while preserving narrative integrity
- Adaptive compression strategies based on content importance

**Stage 21 (Final Production):**
- TTS synthesis for narration tracks
- FCP7 XML export for professional editing workflows
- Asset organization and delivery preparation

Key architectural patterns in these stages:
- **Diagnostic-first approach**: Analyze content before making changes
- **Selective processing**: Only modify content that needs improvement
- **Parallel execution**: Concurrent processing with graceful error handling
- **Quality preservation**: Fallback to original content if refinement fails